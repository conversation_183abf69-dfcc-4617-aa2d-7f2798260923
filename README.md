## 🌈 项目开发环境部署

### 后端技术
- 基于 python3.10 + django4 + mysql8

- 使用软件版本
- python version 3.11.5
- mysql version 8.0.23

### 前端技术

- 基于 vue3

- 使用软件版本
- node version 20.13.0
- vue  version 3.4.31

### 🚧 启动前置软件环境mysql
```bash
# Linux启动mysql数据库服务
systemctl start mysqld

# 配置开机自启动
systemctl enable mysqld
```

### 🚧 项目启动初始化-后端
```bash
# 克隆项目
git clone https://gitee.com/pytests/MangoPlatform.git

# 修改对应的数据库
MangoServer/PyAutoTest/settings/master.py

# MangoServer目录下，安装依赖
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 生成迁移文件
python manage.py makemigrations

# 新建数据库，迁移数据库表
python manage.py migrate

# 运行后端项目
python manage.py runserver

# 后端静态文件打包
python manage.py collectstatic

# 创建超级用户，并按提示输入相应用户名、密码、邮箱
python manage.py createsuperuser

# 接口文档访问地址
http://127.0.0.1:8000/docs
```

### 🚧 项目启动初始化-前端
```bash
# 添加下载源
npm config set registry http://mirrors.cloud.tencent.com/npm/

# 安装yarn
npm install -g yarn

# 进入项目MangoConsole，安装项目依赖
yarn

# 运行前端项目
yarn master

# 前端打包发布
yarn build:prod
```

### 🚧 项目启动初始化-执行器
```bash
# 克隆项目
git clone https://gitee.com/pytests/MangoActuator.git

# MangoActuator目录下，安装依赖
pip install -i https://pypi.tuna.tsinghua.edu.cn/simple -r requirements.txt

# 安装playwright工具
playwright install ffmpeg

# 运行项目
main.py

# 执行器打包
pyinstaller -F -w --name=执行器 --icon=resources/icons/app_icon.png --add-data "resources/icons/app_icon.png;resources/icons" .\main.py
```

### 🎯 快速部署配置（推荐）

#### 自动配置脚本
```bash
# 自动检测IP并配置
./deploy-config.sh

# 或指定特定IP地址
./deploy-config.sh *************
```

### 🚀 启动服务

#### 后端服务
```bash
cd MangoServer
source venv/bin/activate
# 注意：使用0.0.0.0允许外部访问
python manage.py runserver 0.0.0.0:8000
```

#### 前端服务
```bash
cd MangoConsole
npm run master  # 启动master模式
```

### 🌐 访问地址

#### 本地访问
- **前端**: http://localhost:5173
- **后端**: http://localhost:8000
- **接口文档**: http://localhost:8000/docs

#### 局域网访问（同事可访问）
- **前端**: http://[您的IP]:5173
- **后端**: http://[您的IP]:8000
- **接口文档**: http://[您的IP]:8000/docs

### 🔧 灵活配置说明

#### 智能IP检测
前端现在支持智能IP检测，会自动使用当前访问的主机地址作为API服务器地址。

#### 环境配置文件
- `.env.master` - 主分支环境
- `.env.development` - 开发环境
- `.env.production` - 生产环境
- `.env.local.example` - 本地配置示例

#### 不同部署场景
1. **本地开发**: 配置使用 `localhost`
2. **局域网共享**: 配置使用局域网IP (如 `*************`)
3. **服务器部署**: 配置使用服务器公网IP或域名

#### 手动配置API地址
如需手动配置，编辑 `MangoConsole/.env.master` 文件：
```bash
VITE_APP_BASE_URL='http://YOUR_IP:8000'
VITE_APP_SOCKET_URL='ws://YOUR_IP:8000/web/socket?'
```

### 📊 项目状态
- ✅ 后端服务: 支持外部访问 (端口: 8000)
- ✅ 前端服务: 支持局域网访问 (端口: 5173)
- ✅ 智能IP检测: 自动适配部署环境
- 🔧 执行器: 需要单独启动

### 🛠️ 技术栈版本信息
#### 后端
- Python: 3.10+
- Django: 4.2.11
- MySQL: 8.0.23+
- ASGI Server: Daphne 4.2.1

#### 前端
- Node.js: 20.13.0+
- Vue: 3.5.8
- Vite: 4.5.14
- TypeScript: 4.9.5
