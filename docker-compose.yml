# 服务，表示要启动的容器服务
services:
    # 每个服务的名字就是容器的名字
    # 拉取官方mysql镜像
    mysql:
        image: mysql:8.4.2
        ports:
            - "9527:3306"
        restart: always
        container_name: py_mysql
        environment:
            MYSQL_ROOT_PASSWORD: 123456
            MYSQL_DATABASE: mango
        volumes:
            - mysql:/var/lib/mysql

    # 创建django后端镜像
    backend:
        depends_on:
            - mysql
        build: ./MangoServer
        image: py_backend
        container_name: py_backend
        restart: always
        volumes:
            - backend_logs:/app/logs

    # 创建nginx镜像
    nginx:
        depends_on:
            - backend
        build: ./nginx
        image: py_nginx
        container_name: py_nginx
        ports:
            - "5001:81"
        volumes:
            - nginx_logs:/var/log

# 全局声明
volumes:
    mysql:
    backend_logs:
    nginx_logs:
