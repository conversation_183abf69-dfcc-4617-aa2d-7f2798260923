#!/bin/bash

# 芒果自动化测试平台部署配置脚本
# 使用方法: ./deploy-config.sh [IP地址]

echo "🥭 芒果自动化测试平台 - 部署配置工具"
echo "=================================="

# 获取当前机器的IP地址
get_local_ip() {
    # macOS
    if [[ "$OSTYPE" == "darwin"* ]]; then
        ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1
    # Linux
    else
        hostname -I | awk '{print $1}'
    fi
}

# 如果没有提供IP参数，自动检测
if [ -z "$1" ]; then
    LOCAL_IP=$(get_local_ip)
    echo "🔍 自动检测到本机IP: $LOCAL_IP"
    echo "是否使用此IP? (y/n) [默认: y]"
    read -r response
    if [[ "$response" =~ ^[Nn]$ ]]; then
        echo "请输入要使用的IP地址:"
        read -r LOCAL_IP
    fi
else
    LOCAL_IP=$1
fi

echo "📝 配置IP地址: $LOCAL_IP"

# 更新前端配置
echo "🔧 更新前端配置..."
cat > MangoConsole/.env.master << EOF
BUILD_PATH=/
VITE_APP_ENV='master'
VITE_APP_BASE_URL='http://$LOCAL_IP:8000'
VITE_APP_SOCKET_URL='ws://$LOCAL_IP:8000/web/socket?'
VITE_IS_INDEX_WINDOW='false'
EOF

echo "✅ 前端配置已更新"

# 显示访问地址
echo ""
echo "🌐 服务访问地址:"
echo "   前端: http://$LOCAL_IP:5173"
echo "   后端: http://$LOCAL_IP:8000"
echo "   接口文档: http://$LOCAL_IP:8000/docs"
echo ""

# 提示启动命令
echo "🚀 启动服务:"
echo "   后端: cd MangoServer && source venv/bin/activate && python manage.py runserver 0.0.0.0:8000"
echo "   前端: cd MangoConsole && npm run master"
echo ""

echo "✨ 配置完成！同事现在可以通过 http://$LOCAL_IP:5173 访问系统"
