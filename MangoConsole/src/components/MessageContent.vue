<template>
  <a-list :bordered="false" size="small" :style="{ width: '350px' }">
    <a-list-item v-for="(item, index) of data" :key="index">
      <a-list-item-meta :title="item.title" :description="item.description">
        <template #avatar>
          <template v-if="item.status === 1">
            <a-avatar :style="{ backgroundColor: '#2ECC71' }">
              <icon-check />
            </a-avatar>
          </template>
          <template v-else>
            <a-avatar :style="{ backgroundColor: '#E74C3C' }">
              <icon-close />
            </a-avatar>
          </template>
        </template>
      </a-list-item-meta>
    </a-list-item>
  </a-list>
</template>

<script lang="ts" setup>
  import { reactive } from 'vue'

  const data = reactive([
    {
      title: '平台功能未完善',
      description: '平台使用过程中有问题，请联系我！',
      status: 1,
    },
  ])
</script>
<style lang="less" scoped>
  :deep(.arco-list-medium > .arco-list-content > .arco-list-item) {
    padding: 0 !important;
  }
</style>
