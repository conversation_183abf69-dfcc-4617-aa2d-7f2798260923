import Axios, { AxiosResponse } from 'axios'
import qs from 'qs'

// 动态获取API基础URL
function getBaseURL() {
  const envBaseURL = import.meta.env.VITE_APP_BASE_URL

  // 如果环境变量中配置了完整的URL，直接使用
  if (envBaseURL && envBaseURL.startsWith('http')) {
    return envBaseURL
  }

  // 否则使用当前访问的主机地址 + 8000端口
  const protocol = window.location.protocol
  const hostname = window.location.hostname
  return `${protocol}//${hostname}:8000`
}

// 动态获取WebSocket URL
function getWebSocketURL() {
  const envSocketURL = import.meta.env.VITE_APP_SOCKET_URL

  // 如果环境变量中配置了完整的URL，直接使用
  if (envSocketURL && (envSocketURL.startsWith('ws') || envSocketURL.startsWith('wss'))) {
    return envSocketURL
  }

  // 否则使用当前访问的主机地址 + 8000端口
  const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:'
  const hostname = window.location.hostname
  return `${protocol}//${hostname}:8000/web/socket?`
}

export const baseURL = getBaseURL()
export const webSocketURL = getWebSocketURL()

export const CONTENT_TYPE = 'Content-Type'

export const FORM_URLENCODED = 'application/x-www-form-urlencoded; charset=UTF-8'

export const APPLICATION_JSON = 'application/json; charset=UTF-8'

export const TEXT_PLAIN = 'text/plain; charset=UTF-8'

const service = Axios.create({
  baseURL,
  timeout: 10 * 60 * 1000,
})

// 在正式发送请求之前进行拦截配置
service.interceptors.request.use(
  (config) => {
    !config.headers && (config.headers = {})
    if (!config.headers[CONTENT_TYPE]) {
      config.headers[CONTENT_TYPE] = APPLICATION_JSON
    }
    if (config.headers[CONTENT_TYPE] === FORM_URLENCODED) {
      config.data = qs.stringify(config.data)
    }
    return config
  },
  (error) => {
    return Promise.reject(error.response)
  }
)

// 在接口返回数据的时候进行一次拦截
service.interceptors.response.use(
  (response: AxiosResponse): AxiosResponse => {
    if (response.status === 200) {
      return response
    } else {
      throw new Error(response.status.toString())
    }
  },
  (error) => {
    if (error.response.status === 403) {
      return error.response
    }
    if (import.meta.env.MODE === 'development') {
      console.log(error)
    }
    return Promise.reject({ code: 500, msg: '服务器异常，请稍后重试…' })
  }
)

export default service
