@tailwind base;
@tailwind components;
@tailwind utilities;
:root {
  --menu-width: 210px;
}
html,
body,
#app {
  height: 100vh;
  overflow: hidden;
}
* {
  box-sizing: border-box;
}

#nprogress .bar {
  background: rgb(var(--primary-6)) !important;
}

.arco-trigger-menu-icon-suffix::before {
  content: '';
  display: inline-block;
  flex: 1;
}

.form-item__require label::before {
  content: '*';
  color: #f00;
  margin-right: 5px;
}

.form-item__no_require label::before {
  content: '';
  margin-right: 10px;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: inline-block;
  vertical-align: baseline;
}

.card-border-radius {
  border-radius: var(--border-radius-large) !important;
}
.card-border-radius:hover {
  box-shadow: 0px 0px 10px #ddd;
}

.arco-pagination-item-active {
  color: var(--color-white) !important;
}

.arco-pagination-item-active:hover {
  color: var(--color-white) !important;
}

.arco-btn-disabled {
  background-color: rgba(var(--primary-1), 0.6) !important;
}

/* .arco-trigger-popup{
  width: 150px;
} */
