:root {
  --nprogress-color: @primary-color;
}

/* 滚动条凹槽的颜色，还可以设置边框属性 */
*::-webkit-scrollbar-track-piece {
  border-radius: 2em;
  background-color: transparent;
}

body {
  width: 100%;
  height: 100%;
  margin: 0;
  padding: 0;
  overflow: hidden;
  font-family: 'PingFang SC', 'Helvetica Neue', Helvetica, 'Hiragino Sans GB', 'Microsoft YaHei',
    Arial, sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  -webkit-tap-highlight-color: transparent;
  -webkit-overflow-scrolling: touch;
}

li:not(.ant-image-preview-operations-operation) {
  list-style: auto !important;
}

i {
  font-style: normal;
}

input {
  border: none;
  outline: none;

  &:-webkit-autofill {
    box-shadow: 0 0 0 1000px #e6e8ed inset !important;
  }
}

button {
  border: 0;
  outline: none;
  appearance: none;
}

label {
  font-weight: 700;
}

*,
*::before,
*::after {
  box-sizing: inherit;
}

div:focus,
span:focus,
i:focus {
  outline: none;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  color: inherit;
  text-decoration: none;
  cursor: pointer;
}

div:focus,
span:focus,
i:focus {
  outline: none;
}

h3 {
  margin: 0;
  padding: 0;
  font-size: 18px;
  line-height: 26px;
}

p {
  margin: 0;
  padding: 0;
}

.common-container {
  position: relative;
  width: 100%;
  height: 100%;
  padding: 0 24px;
}

@media (width >= 1900px) {
  .common-container,
  .layout-header {
    width: 1440px !important;
    margin: 0 auto;
  }
}

.common-header {
  display: flex;
  position: sticky;
  z-index: 10;
  top: 0;
  align-items: center;
  height: var(--header-height);
  margin-bottom: 6px;
  padding: 0 24px;
  background-color: #f4f5f9;
  font-size: 22px;
  line-height: var(--header-height);

  span {
    font-weight: 500;
  }
}

// 适用于主内容区灰底色，上下布局，下部自适应，白色背景，衔接处10px边距
.common-container-grey {
  display: flex;
  position: relative;
  flex-direction: column;
  width: 100%;
  height: 100%;
  background-color: #f3f3f3;

  &__header {
    margin-bottom: 10px;
    padding: 22px 24px;
    background-color: #fff;
  }

  &__block {
    margin-bottom: 10px;
    padding: 24px;
    background-color: #fff;

    .title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      padding-bottom: 10px;
      font-size: 16px;
      font-weight: 500;
    }
  }

  &__body {
    flex: 1;
    padding: 22px 16px;
    background-color: #fff;
  }

  // 适用于主内容区灰底色，上下布局下部自适应，白色背景，衔接处10px边距，下半部分内左右分区，左边自适应
  &__body_partition {
    display: flex;
    flex: 1;

    .lf {
      flex: 1;
      margin-right: 10px;
      padding: 22px 16px;
      background-color: #fff;
    }

    .rf {
      padding: 22px 16px;
      background-color: #fff;
    }
  }
}

.gk-window {
  .msg-resource-messge,
  .msg-content-messge {
    margin-left: 0 !important;
  }

  .guide-box {
    .guide-title {
      font-size: 24px !important;
    }

    .guide-subtitle {
      font-size: 20px !important;
    }
  }
}
