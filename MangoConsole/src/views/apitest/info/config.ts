import { FormItem } from '@/types/components'
import { reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import { useTable, useTableColumn } from '@/hooks/table'
const table = useTable()
import parseCurl from 'parse-curl'

export const conditionItems: Array<FormItem> = reactive([
  {
    key: 'name',
    label: '接口名称',
    type: 'input',
    placeholder: '请输入接口名称',
    value: '',
    reset: function () {
      this.value = ''
    },
  },
  {
    key: 'url',
    label: '接口URL',
    value: '',
    type: 'input',
    placeholder: '请输入接口地址',
    reset: function () {},
  },
  {
    key: 'project_product',
    label: '产品所属',
    value: '',
    type: 'select',
    placeholder: '请选择所属产品',
    optionItems: [],
    reset: function () {},
  },
  {
    key: 'module',
    label: '所属模块',
    value: '',
    type: 'select',
    placeholder: '请选择所属产品',
    optionItems: [],
    reset: function () {},
  },
  {
    key: 'status',
    label: '接口状态',
    value: '',
    type: 'select',
    placeholder: '请选择接口状态',
    optionItems: [],
    reset: function () {},
  },
])
export const formItems: FormItem[] = reactive([
  {
    label: '项目/产品',
    key: 'project_product',
    value: '',
    placeholder: '请选择产品名称',
    required: true,
    type: 'cascader',
    validator: function () {
      if (!this.value && this.value !== 0) {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
  {
    label: '模块名称',
    key: 'module',
    value: '',
    type: 'select',
    required: true,
    placeholder: '请选择所属模块',
    validator: function () {
      if (!this.value && this.value !== 0) {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
  {
    label: '接口名称',
    key: 'name',
    value: '',
    type: 'input',
    required: true,
    placeholder: '请输入接口名称',
    validator: function () {
      if (!this.value && this.value !== '0') {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },

  {
    label: '接口URL',
    key: 'url',
    value: '',
    type: 'input',
    required: true,
    placeholder: '请输入url地址',
  },
  {
    label: '请求方法',
    key: 'method',
    value: '',
    type: 'select',
    required: true,
    placeholder: '请选择接口方法',
    validator: function () {
      if (!this.value && this.value !== 0) {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
])
export const formItemsImport: FormItem[] = reactive([
  {
    label: '项目/产品',
    key: 'project_product',
    value: '',
    placeholder: '请选择产品名称',
    required: true,
    type: 'cascader',
    validator: function () {
      if (!this.value && this.value !== 0) {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
  {
    label: '模块名称',
    key: 'module',
    value: '',
    type: 'select',
    required: true,
    placeholder: '请选择所属模块',
    validator: function () {
      if (!this.value && this.value !== 0) {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
  {
    label: '接口名称',
    key: 'name',
    value: '',
    type: 'input',
    required: true,
    placeholder: '请输入接口名称',
    validator: function () {
      if (!this.value && this.value !== '0') {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
  {
    label: 'curl',
    key: 'curl',
    value: '',
    type: 'textarea',
    required: true,
    placeholder: '请输入复制的curl',
    validator: function () {
      if (!this.value && this.value !== 0) {
        Message.error(this.placeholder || '')
        return false
      }
      const parsedCurl = parseCurl(this.value)
      const dataRaw = parseDataRaw(this.value)
      this.value = { ...parsedCurl, data: dataRaw }
      return true
    },
  },
])
function parseDataRaw(curlCommand: string) {
  const dataRawIndex = curlCommand.indexOf('--data-raw')
  if (dataRawIndex !== -1) {
    const dataRawValue: string = curlCommand.substring(dataRawIndex + '--data-raw '.length)
    // return JSON.parse(dataRawValue)
    return dataRawValue
  }
  return {}
}
export const tableColumns = useTableColumn([
  table.indexColumn,
  {
    title: '项目/产品',
    key: 'project_product',
    dataIndex: 'project_product',
    width: 180,
  },
  {
    title: '模块名称',
    key: 'module',
    dataIndex: 'module',
    width: 180,
  },
  {
    title: '接口名称',
    key: 'name',
    dataIndex: 'name',
    width: 270,
  },
  {
    title: '客户端类型',
    key: 'client',
    dataIndex: 'client',
    width: 110,
  },
  {
    title: '接口URL',
    key: 'url',
    dataIndex: 'url',
    ellipsis: true,
    tooltip: true,
  },
  {
    title: '请求方法',
    key: 'method',
    dataIndex: 'method',
    width: 100,
  },
  {
    title: '接口状态',
    key: 'status',
    dataIndex: 'status',
    width: 100,
  },
  {
    title: '操作',
    key: 'actions',
    dataIndex: 'actions',
    width: 170,
  },
])
