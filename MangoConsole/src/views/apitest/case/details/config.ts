import { FormItem } from '@/types/components'
import { reactive } from 'vue'
import { Message } from '@arco-design/web-vue'
import centerTitle from "@/views/index/components/CenterTitle.vue";

export const columns = reactive([
  {
    title: '接口名称',
    dataIndex: 'name',
  },

  {
    title: '请求方法',
    dataIndex: 'method',
  },
  {
    title: '测试结果',
    dataIndex: 'status',
    align: 'center',
  },
  {
    title: '操作',
    dataIndex: 'actions',
    align: 'center',
    width: 220,
  },
])
export const formItems: FormItem[] = reactive([
  {
    label: '模块名称',
    key: 'module',
    value: '',
    placeholder: '请选择所属模块',
    required: true,
    type: 'cascader',
    validator: function () {
      if (!this.value) {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
  {
    label: '接口名称',
    key: 'api_info',
    value: '',
    placeholder: '请选择接口',
    required: true,
    type: 'select',
    validator: function () {
      if (!this.value) {
        Message.error(this.placeholder || '')
        return false
      }
      return true
    },
  },
])
