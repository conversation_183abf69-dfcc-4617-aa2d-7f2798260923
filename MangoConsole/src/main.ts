import { createApp } from 'vue'
import App from './App.vue'
import { setupRouter } from './router'
import './styles'
import { setupGlobalComponent } from '@/layouts'
import { setupPinia } from '@/store/pinia'
import setupRouterGuard from './router/guard'

async function setup() {
  const app = createApp(App)
  setupPinia(app)
  setupRouter(app)
  setupRouterGuard()
  setupGlobalComponent(app)
  app.mount('#app')
}

// 启动项目
setup()
