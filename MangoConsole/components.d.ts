/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AAffix: typeof import('@arco-design/web-vue')['Affix']
    AAvatar: typeof import('@arco-design/web-vue')['Avatar']
    ABackTop: typeof import('@arco-design/web-vue')['BackTop']
    ABadge: typeof import('@arco-design/web-vue')['Badge']
    ABreadcrumb: typeof import('@arco-design/web-vue')['Breadcrumb']
    ABreadcrumbItem: typeof import('@arco-design/web-vue')['BreadcrumbItem']
    AButton: typeof import('@arco-design/web-vue')['Button']
    ACard: typeof import('@arco-design/web-vue')['Card']
    ACascader: typeof import('@arco-design/web-vue')['Cascader']
    ACheckbox: typeof import('@arco-design/web-vue')['Checkbox']
    ACheckboxGroup: typeof import('@arco-design/web-vue')['CheckboxGroup']
    ACol: typeof import('@arco-design/web-vue')['Col']
    ACollapse: typeof import('@arco-design/web-vue')['Collapse']
    ACollapseItem: typeof import('@arco-design/web-vue')['CollapseItem']
    ADatePicker: typeof import('@arco-design/web-vue')['DatePicker']
    AddButton: typeof import('./src/components/AddButton.vue')['default']
    ADescriptions: typeof import('@arco-design/web-vue')['Descriptions']
    ADivider: typeof import('@arco-design/web-vue')['Divider']
    ADoption: typeof import('@arco-design/web-vue')['Doption']
    ADrawer: typeof import('@arco-design/web-vue')['Drawer']
    ADropdown: typeof import('@arco-design/web-vue')['Dropdown']
    AForm: typeof import('@arco-design/web-vue')['Form']
    AFormItem: typeof import('@arco-design/web-vue')['FormItem']
    AImage: typeof import('@arco-design/web-vue')['Image']
    AInput: typeof import('@arco-design/web-vue')['Input']
    AInputNumber: typeof import('@arco-design/web-vue')['InputNumber']
    AInputPassword: typeof import('@arco-design/web-vue')['InputPassword']
    AInputTag: typeof import('@arco-design/web-vue')['InputTag']
    ALink: typeof import('@arco-design/web-vue')['Link']
    AList: typeof import('@arco-design/web-vue')['List']
    AListItem: typeof import('@arco-design/web-vue')['ListItem']
    AListItemMeta: typeof import('@arco-design/web-vue')['ListItemMeta']
    AMenu: typeof import('@arco-design/web-vue')['Menu']
    AMenuItem: typeof import('@arco-design/web-vue')['MenuItem']
    AModal: typeof import('@arco-design/web-vue')['Modal']
    AOption: typeof import('@arco-design/web-vue')['Option']
    APagination: typeof import('@arco-design/web-vue')['Pagination']
    APopover: typeof import('@arco-design/web-vue')['Popover']
    ARadioGroup: typeof import('@arco-design/web-vue')['RadioGroup']
    ARow: typeof import('@arco-design/web-vue')['Row']
    ASelect: typeof import('@arco-design/web-vue')['Select']
    ASpace: typeof import('@arco-design/web-vue')['Space']
    ASpin: typeof import('@arco-design/web-vue')['Spin']
    AStatistic: typeof import('@arco-design/web-vue')['Statistic']
    ASubMenu: typeof import('@arco-design/web-vue')['SubMenu']
    ASwitch: typeof import('@arco-design/web-vue')['Switch']
    ATable: typeof import('@arco-design/web-vue')['Table']
    ATableColumn: typeof import('@arco-design/web-vue')['TableColumn']
    ATabPane: typeof import('@arco-design/web-vue')['TabPane']
    ATabs: typeof import('@arco-design/web-vue')['Tabs']
    ATag: typeof import('@arco-design/web-vue')['Tag']
    ATextarea: typeof import('@arco-design/web-vue')['Textarea']
    ATimePicker: typeof import('@arco-design/web-vue')['TimePicker']
    ATooltip: typeof import('@arco-design/web-vue')['Tooltip']
    ATree: typeof import('@arco-design/web-vue')['Tree']
    ATreeSelect: typeof import('@arco-design/web-vue')['TreeSelect']
    AUpload: typeof import('@arco-design/web-vue')['Upload']
    DeleteButton: typeof import('./src/components/DeleteButton.vue')['default']
    MessageContent: typeof import('./src/components/MessageContent.vue')['default']
    ModalDialog: typeof import('./src/components/ModalDialog.vue')['default']
    RichTextEditor: typeof import('./src/components/RichTextEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    Scrollbar: typeof import('./src/components/Scrollbar/src/Scrollbar.vue')['default']
    SearchContent: typeof import('./src/components/SearchContent.vue')['default']
    TableBody: typeof import('./src/components/TableBody.vue')['default']
    TableFooter: typeof import('./src/components/TableFooter.vue')['default']
    TableHeader: typeof import('./src/components/TableHeader.vue')['default']
  }
}
