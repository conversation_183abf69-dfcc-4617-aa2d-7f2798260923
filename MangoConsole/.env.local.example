# 本地开发环境配置示例
# 复制此文件为 .env.local 并根据实际情况修改

BUILD_PATH=/
VITE_APP_ENV='local'

# 方式1: 使用具体IP地址（适合固定IP环境）
# VITE_APP_BASE_URL='http://*************:8000'
# VITE_APP_SOCKET_URL='ws://*************:8000/web/socket?'

# 方式2: 使用localhost（仅本机访问）
VITE_APP_BASE_URL='http://localhost:8000'
VITE_APP_SOCKET_URL='ws://localhost:8000/web/socket?'

# 方式3: 留空让系统自动检测（推荐）
# VITE_APP_BASE_URL=''
# VITE_APP_SOCKET_URL=''

VITE_IS_INDEX_WINDOW='false'
