{"name": "mango", "version": "0.1.0", "scripts": {"dev": "vite --mode development --host", "prod": "vite --mode production --host", "master": "vite --mode master --host", "build:dev": "vite build --mode development", "build:prod": "vite build --mode production", "build:master": "vite build --mode master", "staging": "vite build --mode staging", "tsc": "vue-tsc --noEmit", "serve": "vite preview"}, "dependencies": {"@logicflow/core": "^0.7.16", "@logicflow/extension": "^0.7.16", "@vueuse/core": "^10.7.2", "axios": "1.6.2", "cropperjs": "^1.6.1", "curl-to-fetch": "^1.2.1", "curl-to-json": "^1.0.24", "echarts": "^5.4.3", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "parse-curl": "^0.2.6", "parse-curl-js": "^1.0.3", "path-browserify": "^1.0.1", "pinia": "^2.2.2", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "quill": "^1.3.7", "safer-buffer": "^2.1.2", "sortablejs": "^1.15.3", "tiny-emitter": "^2.1.0", "vue": "^3.5.8", "vue-router": "^4.4.5", "vuedraggable": "^4.1.0", "xgplayer": "^2.32.5", "xlsx": "^0.17.5"}, "devDependencies": {"@arco-design/web-vue": "^2.56.2", "@types/lodash-es": "^4.17.10", "@types/nprogress": "^0.2.2", "@types/path-browserify": "^1.0.1", "@types/qrcode": "^1.5.4", "@types/qs": "^6.9.16", "@types/quill": "^2.0.13", "@types/sortablejs": "^1.15.4", "@typescript-eslint/eslint-plugin": "^5.62.0", "@typescript-eslint/parser": "^5.62.0", "@vitejs/plugin-vue": "^3.1.2", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.16", "dotenv": "^12.0.4", "eslint": "^7.32.0", "eslint-config-prettier": "^8.10.0", "eslint-define-config": "^1.24.1", "eslint-plugin-prettier": "^3.4.1", "eslint-plugin-vue": "^7.20.0", "less": "^4.2.0", "less-loader": "^10.2.0", "postcss": "^8.4.47", "prettier": "^2.8.8", "tailwindcss": "^3.4.13", "typescript": "^4.9.5", "unplugin-vue-components": "^0.26.0", "vite": "^4.5.3", "vite-plugin-svg-icons": "^1.0.5", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^0.3.0"}}