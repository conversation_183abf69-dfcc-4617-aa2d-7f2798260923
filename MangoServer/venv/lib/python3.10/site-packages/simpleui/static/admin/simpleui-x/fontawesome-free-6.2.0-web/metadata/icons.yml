'0':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '0'
  search:
    terms: []
  styles:
    - solid
  unicode: '30'
  voted: false
'1':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '1'
  search:
    terms: []
  styles:
    - solid
  unicode: '31'
  voted: false
'2':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '2'
  search:
    terms: []
  styles:
    - solid
  unicode: '32'
  voted: false
'3':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '3'
  search:
    terms: []
  styles:
    - solid
  unicode: '33'
  voted: false
'4':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '4'
  search:
    terms: []
  styles:
    - solid
  unicode: '34'
  voted: false
'5':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '5'
  search:
    terms: []
  styles:
    - solid
  unicode: '35'
  voted: false
'6':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '6'
  search:
    terms: []
  styles:
    - solid
  unicode: '36'
  voted: false
'7':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '7'
  search:
    terms: []
  styles:
    - solid
  unicode: '37'
  voted: false
'8':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '8'
  search:
    terms: []
  styles:
    - solid
  unicode: '38'
  voted: false
'9':
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: '9'
  search:
    terms: []
  styles:
    - solid
  unicode: '39'
  voted: false
42-group:
  aliases:
    names:
      - innosoft
  changes:
    - 5.15.0
    - 6.0.0-beta2
  label: 42.group
  search:
    terms: []
  styles:
    - brands
  unicode: e080
  voted: false
500px:
  changes:
    - 4.4.0
    - 5.0.0
  label: 500px
  search:
    terms: []
  styles:
    - brands
  unicode: f26e
  voted: false
a:
  aliases:
    unicodes:
      composite:
        - '61'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: A
  search:
    terms: []
  styles:
    - solid
  unicode: '41'
  voted: false
accessible-icon:
  aliases:
    unicodes:
      composite:
        - f29b
  changes:
    - 5.0.0
  label: Accessible Icon
  search:
    terms: []
  styles:
    - brands
  unicode: f368
  voted: false
accusoft:
  changes:
    - 5.0.0
  label: Accusoft
  search:
    terms: []
  styles:
    - brands
  unicode: f369
  voted: false
address-book:
  aliases:
    names:
      - contact-book
    unicodes:
      composite:
        - f2ba
      secondary:
        - 10f2b9
  changes:
    - 4.7.0
    - 5.0.0
    - 5.0.3
    - 6.0.0-beta1
    - 6.2.0
  label: Address Book
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2b9
  voted: false
address-card:
  aliases:
    names:
      - contact-card
      - vcard
    unicodes:
      composite:
        - f2bc
      secondary:
        - 10f2bb
  changes:
    - 4.7.0
    - 5.0.0
    - 5.0.3
    - 6.0.0-beta1
    - 6.2.0
  label: Address Card
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2bb
  voted: false
adn:
  changes:
    - 3.2.0
    - 5.0.0
  label: App.net
  search:
    terms: []
  styles:
    - brands
  unicode: f170
  voted: false
adversal:
  changes:
    - 5.0.0
  label: Adversal
  search:
    terms: []
  styles:
    - brands
  unicode: f36a
  voted: false
affiliatetheme:
  changes:
    - 5.0.0
  label: affiliatetheme
  search:
    terms: []
  styles:
    - brands
  unicode: f36b
  voted: false
airbnb:
  changes:
    - 5.8.0
  label: Airbnb
  search:
    terms: []
  styles:
    - brands
  unicode: f834
  voted: false
algolia:
  changes:
    - 5.0.0
  label: Algolia
  search:
    terms: []
  styles:
    - brands
  unicode: f36c
  voted: false
align-center:
  aliases:
    unicodes:
      secondary:
        - 10f037
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: align-center
  search:
    terms: []
  styles:
    - solid
  unicode: f037
  voted: false
align-justify:
  aliases:
    unicodes:
      secondary:
        - 10f039
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: align-justify
  search:
    terms: []
  styles:
    - solid
  unicode: f039
  voted: false
align-left:
  aliases:
    unicodes:
      secondary:
        - 10f036
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: align-left
  search:
    terms: []
  styles:
    - solid
  unicode: f036
  voted: false
align-right:
  aliases:
    unicodes:
      secondary:
        - 10f038
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: align-right
  search:
    terms: []
  styles:
    - solid
  unicode: f038
  voted: false
alipay:
  changes:
    - 5.3.0
  label: Alipay
  search:
    terms: []
  styles:
    - brands
  unicode: f642
  voted: false
amazon:
  changes:
    - 4.4.0
    - 5.0.0
  label: Amazon
  search:
    terms: []
  styles:
    - brands
  unicode: f270
  voted: false
amazon-pay:
  changes:
    - 5.0.2
    - 5.8.0
  label: Amazon Pay
  search:
    terms: []
  styles:
    - brands
  unicode: f42c
  voted: false
amilia:
  changes:
    - 5.0.0
  label: Amilia
  search:
    terms: []
  styles:
    - brands
  unicode: f36d
  voted: false
anchor:
  aliases:
    unicodes:
      composite:
        - '2693'
      secondary:
        - 10f13d
  changes:
    - 3.1.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Anchor
  search:
    terms: []
  styles:
    - solid
  unicode: f13d
  voted: false
anchor-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Anchor Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e4aa
  voted: false
anchor-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Anchor Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e4ab
  voted: false
anchor-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Anchor Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e4ac
  voted: false
anchor-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: Anchor Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e4ad
  voted: false
android:
  changes:
    - 3.2.0
    - 5.0.0
    - 5.12.0
  label: Android
  search:
    terms: []
  styles:
    - brands
  unicode: f17b
  voted: false
angellist:
  changes:
    - 4.2.0
    - 5.0.0
  label: AngelList
  search:
    terms: []
  styles:
    - brands
  unicode: f209
  voted: false
angle-down:
  aliases:
    unicodes:
      composite:
        - '2304'
      secondary:
        - 10f107
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: angle-down
  search:
    terms: []
  styles:
    - solid
  unicode: f107
  voted: false
angle-left:
  aliases:
    unicodes:
      composite:
        - '2039'
      secondary:
        - 10f104
  changes:
    - 3.0.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: angle-left
  search:
    terms: []
  styles:
    - solid
  unicode: f104
  voted: false
angle-right:
  aliases:
    unicodes:
      composite:
        - 203a
      secondary:
        - 10f105
  changes:
    - 3.0.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: angle-right
  search:
    terms: []
  styles:
    - solid
  unicode: f105
  voted: false
angle-up:
  aliases:
    unicodes:
      composite:
        - '2303'
      secondary:
        - 10f106
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: angle-up
  search:
    terms: []
  styles:
    - solid
  unicode: f106
  voted: false
angles-down:
  aliases:
    names:
      - angle-double-down
    unicodes:
      secondary:
        - 10f103
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Angles down
  search:
    terms: []
  styles:
    - solid
  unicode: f103
  voted: false
angles-left:
  aliases:
    names:
      - angle-double-left
    unicodes:
      composite:
        - ab
      secondary:
        - 10f100
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Angles left
  search:
    terms: []
  styles:
    - solid
  unicode: f100
  voted: false
angles-right:
  aliases:
    names:
      - angle-double-right
    unicodes:
      composite:
        - bb
      secondary:
        - 10f101
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Angles right
  search:
    terms: []
  styles:
    - solid
  unicode: f101
  voted: false
angles-up:
  aliases:
    names:
      - angle-double-up
    unicodes:
      secondary:
        - 10f102
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Angles up
  search:
    terms: []
  styles:
    - solid
  unicode: f102
  voted: false
angrycreative:
  changes:
    - 5.0.0
  label: Angry Creative
  search:
    terms: []
  styles:
    - brands
  unicode: f36e
  voted: false
angular:
  changes:
    - 5.0.0
    - 5.8.0
  label: Angular
  search:
    terms: []
  styles:
    - brands
  unicode: f420
  voted: false
ankh:
  aliases:
    unicodes:
      composite:
        - '2625'
      secondary:
        - 10f644
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Ankh
  search:
    terms: []
  styles:
    - solid
  unicode: f644
  voted: false
app-store:
  changes:
    - 5.0.0
  label: App Store
  search:
    terms: []
  styles:
    - brands
  unicode: f36f
  voted: false
app-store-ios:
  changes:
    - 5.0.0
  label: iOS App Store
  search:
    terms: []
  styles:
    - brands
  unicode: f370
  voted: false
apper:
  changes:
    - 5.0.0
  label: Apper Systems AB
  search:
    terms: []
  styles:
    - brands
  unicode: f371
  voted: false
apple:
  changes:
    - 3.2.0
    - 5.0.0
    - 5.0.7
    - 5.8.0
  label: Apple
  search:
    terms: []
  styles:
    - brands
  unicode: f179
  voted: false
apple-pay:
  changes:
    - 5.0.0
  label: Apple Pay
  search:
    terms: []
  styles:
    - brands
  unicode: f415
  voted: true
apple-whole:
  aliases:
    names:
      - apple-alt
    unicodes:
      composite:
        - 1f34e
        - 1f34f
      secondary:
        - 10f5d1
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Apple whole
  search:
    terms: []
  styles:
    - solid
  unicode: f5d1
  voted: false
archway:
  aliases:
    unicodes:
      secondary:
        - 10f557
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Archway
  search:
    terms: []
  styles:
    - solid
  unicode: f557
  voted: false
arrow-down:
  aliases:
    unicodes:
      composite:
        - '2193'
      secondary:
        - 10f063
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Arrow down
  search:
    terms: []
  styles:
    - solid
  unicode: f063
  voted: false
arrow-down-1-9:
  aliases:
    names:
      - sort-numeric-asc
      - sort-numeric-down
    unicodes:
      secondary:
        - 10f162
  changes:
    - 3.2.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow down 1 9
  search:
    terms: []
  styles:
    - solid
  unicode: f162
  voted: false
arrow-down-9-1:
  aliases:
    names:
      - sort-numeric-desc
      - sort-numeric-down-alt
    unicodes:
      secondary:
        - 10f886
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow down 9 1
  search:
    terms: []
  styles:
    - solid
  unicode: f886
  voted: false
arrow-down-a-z:
  aliases:
    names:
      - sort-alpha-asc
      - sort-alpha-down
    unicodes:
      secondary:
        - 10f15d
  changes:
    - 3.2.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow down a z
  search:
    terms: []
  styles:
    - solid
  unicode: f15d
  voted: false
arrow-down-long:
  aliases:
    names:
      - long-arrow-down
    unicodes:
      secondary:
        - 10f175
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow down long
  search:
    terms: []
  styles:
    - solid
  unicode: f175
  voted: false
arrow-down-short-wide:
  aliases:
    names:
      - sort-amount-desc
      - sort-amount-down-alt
    unicodes:
      secondary:
        - 10f884
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow down short wide
  search:
    terms: []
  styles:
    - solid
  unicode: f884
  voted: false
arrow-down-up-across-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrow Down-up-across-line
  search:
    terms: []
  styles:
    - solid
  unicode: e4af
  voted: false
arrow-down-up-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrow Down-up-lock
  search:
    terms: []
  styles:
    - solid
  unicode: e4b0
  voted: false
arrow-down-wide-short:
  aliases:
    names:
      - sort-amount-asc
      - sort-amount-down
    unicodes:
      secondary:
        - 10f160
  changes:
    - 3.2.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow down wide short
  search:
    terms: []
  styles:
    - solid
  unicode: f160
  voted: false
arrow-down-z-a:
  aliases:
    names:
      - sort-alpha-desc
      - sort-alpha-down-alt
    unicodes:
      secondary:
        - 10f881
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow down z a
  search:
    terms: []
  styles:
    - solid
  unicode: f881
  voted: false
arrow-left:
  aliases:
    unicodes:
      composite:
        - '2190'
      secondary:
        - 10f060
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: arrow-left
  search:
    terms: []
  styles:
    - solid
  unicode: f060
  voted: false
arrow-left-long:
  aliases:
    names:
      - long-arrow-left
    unicodes:
      secondary:
        - 10f177
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow left long
  search:
    terms: []
  styles:
    - solid
  unicode: f177
  voted: false
arrow-pointer:
  aliases:
    names:
      - mouse-pointer
    unicodes:
      secondary:
        - 10f245
  changes:
    - 4.4.0
    - 5.0.0
    - 5.0.3
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Arrow pointer
  search:
    terms: []
  styles:
    - solid
  unicode: f245
  voted: false
arrow-right:
  aliases:
    unicodes:
      composite:
        - '2192'
      secondary:
        - 10f061
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: arrow right
  search:
    terms: []
  styles:
    - solid
  unicode: f061
  voted: false
arrow-right-arrow-left:
  aliases:
    names:
      - exchange
    unicodes:
      composite:
        - 21c4
      secondary:
        - 10f0ec
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow right arrow left
  search:
    terms: []
  styles:
    - solid
  unicode: f0ec
  voted: false
arrow-right-from-bracket:
  aliases:
    names:
      - sign-out
    unicodes:
      secondary:
        - 10f08b
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow right from bracket
  search:
    terms: []
  styles:
    - solid
  unicode: f08b
  voted: false
arrow-right-long:
  aliases:
    names:
      - long-arrow-right
    unicodes:
      secondary:
        - 10f178
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow right long
  search:
    terms: []
  styles:
    - solid
  unicode: f178
  voted: false
arrow-right-to-bracket:
  aliases:
    names:
      - sign-in
    unicodes:
      secondary:
        - 10f090
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow right to bracket
  search:
    terms: []
  styles:
    - solid
  unicode: f090
  voted: false
arrow-right-to-city:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrow Right-to-city
  search:
    terms: []
  styles:
    - solid
  unicode: e4b3
  voted: false
arrow-rotate-left:
  aliases:
    names:
      - arrow-left-rotate
      - arrow-rotate-back
      - arrow-rotate-backward
      - undo
    unicodes:
      composite:
        - 21ba
      secondary:
        - 10f0e2
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow Rotate Left
  search:
    terms: []
  styles:
    - solid
  unicode: f0e2
  voted: false
arrow-rotate-right:
  aliases:
    names:
      - arrow-right-rotate
      - arrow-rotate-forward
      - redo
    unicodes:
      composite:
        - 21bb
      secondary:
        - 10f01e
  changes:
    - 1.0.0
    - 5.0.0
    - 5.8.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow Rotate Right
  search:
    terms: []
  styles:
    - solid
  unicode: f01e
  voted: false
arrow-trend-down:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Arrow trend down
  search:
    terms: []
  styles:
    - solid
  unicode: e097
  voted: false
arrow-trend-up:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Arrow trend up
  search:
    terms: []
  styles:
    - solid
  unicode: e098
  voted: false
arrow-turn-down:
  aliases:
    names:
      - level-down
    unicodes:
      secondary:
        - 10f149
  changes:
    - 3.1.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow turn down
  search:
    terms: []
  styles:
    - solid
  unicode: f149
  voted: false
arrow-turn-up:
  aliases:
    names:
      - level-up
    unicodes:
      secondary:
        - 10f148
  changes:
    - 3.1.0
    - 5.0.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow turn up
  search:
    terms: []
  styles:
    - solid
  unicode: f148
  voted: false
arrow-up:
  aliases:
    unicodes:
      composite:
        - '2191'
      secondary:
        - 10f062
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Arrow up
  search:
    terms: []
  styles:
    - solid
  unicode: f062
  voted: false
arrow-up-1-9:
  aliases:
    names:
      - sort-numeric-up
    unicodes:
      secondary:
        - 10f163
  changes:
    - 3.2.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up 1 9
  search:
    terms: []
  styles:
    - solid
  unicode: f163
  voted: false
arrow-up-9-1:
  aliases:
    names:
      - sort-numeric-up-alt
    unicodes:
      secondary:
        - 10f887
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up 9 1
  search:
    terms: []
  styles:
    - solid
  unicode: f887
  voted: false
arrow-up-a-z:
  aliases:
    names:
      - sort-alpha-up
    unicodes:
      secondary:
        - 10f15e
  changes:
    - 3.2.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up a z
  search:
    terms: []
  styles:
    - solid
  unicode: f15e
  voted: false
arrow-up-from-bracket:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up from bracket
  search:
    terms: []
  styles:
    - solid
  unicode: e09a
  voted: false
arrow-up-from-ground-water:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrow Up-from-ground-water
  search:
    terms: []
  styles:
    - solid
  unicode: e4b5
  voted: false
arrow-up-from-water-pump:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrow Up-from-water-pump
  search:
    terms: []
  styles:
    - solid
  unicode: e4b6
  voted: false
arrow-up-long:
  aliases:
    names:
      - long-arrow-up
    unicodes:
      secondary:
        - 10f176
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up long
  search:
    terms: []
  styles:
    - solid
  unicode: f176
  voted: false
arrow-up-right-dots:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrow Up-right-dots
  search:
    terms: []
  styles:
    - solid
  unicode: e4b7
  voted: false
arrow-up-right-from-square:
  aliases:
    names:
      - external-link
    unicodes:
      secondary:
        - 10f08e
  changes:
    - 1.0.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Arrow up right from square
  search:
    terms: []
  styles:
    - solid
  unicode: f08e
  voted: false
arrow-up-short-wide:
  aliases:
    names:
      - sort-amount-up-alt
    unicodes:
      secondary:
        - 10f885
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up short wide
  search:
    terms: []
  styles:
    - solid
  unicode: f885
  voted: false
arrow-up-wide-short:
  aliases:
    names:
      - sort-amount-up
    unicodes:
      secondary:
        - 10f161
  changes:
    - 3.2.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up wide short
  search:
    terms: []
  styles:
    - solid
  unicode: f161
  voted: false
arrow-up-z-a:
  aliases:
    names:
      - sort-alpha-up-alt
    unicodes:
      secondary:
        - 10f882
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrow up z a
  search:
    terms: []
  styles:
    - solid
  unicode: f882
  voted: false
arrows-down-to-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Down-to-line
  search:
    terms: []
  styles:
    - solid
  unicode: e4b8
  voted: false
arrows-down-to-people:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Down-to-people
  search:
    terms: []
  styles:
    - solid
  unicode: e4b9
  voted: false
arrows-left-right:
  aliases:
    names:
      - arrows-h
    unicodes:
      secondary:
        - 10f07e
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrows left right
  search:
    terms: []
  styles:
    - solid
  unicode: f07e
  voted: false
arrows-left-right-to-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Left-right-to-line
  search:
    terms: []
  styles:
    - solid
  unicode: e4ba
  voted: false
arrows-rotate:
  aliases:
    names:
      - refresh
      - sync
    unicodes:
      composite:
        - 1f5d8
      secondary:
        - 10f021
  changes:
    - 1.0.0
    - 5.0.0
    - 5.8.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrows rotate
  search:
    terms: []
  styles:
    - solid
  unicode: f021
  voted: false
arrows-spin:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Spin
  search:
    terms: []
  styles:
    - solid
  unicode: e4bb
  voted: false
arrows-split-up-and-left:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Split-up-and-left
  search:
    terms: []
  styles:
    - solid
  unicode: e4bc
  voted: false
arrows-to-circle:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows To-circle
  search:
    terms: []
  styles:
    - solid
  unicode: e4bd
  voted: false
arrows-to-dot:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows To-dot
  search:
    terms: []
  styles:
    - solid
  unicode: e4be
  voted: false
arrows-to-eye:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows To-eye
  search:
    terms: []
  styles:
    - solid
  unicode: e4bf
  voted: false
arrows-turn-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Turn-right
  search:
    terms: []
  styles:
    - solid
  unicode: e4c0
  voted: false
arrows-turn-to-dots:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Turn-to-dots
  search:
    terms: []
  styles:
    - solid
  unicode: e4c1
  voted: false
arrows-up-down:
  aliases:
    names:
      - arrows-v
    unicodes:
      secondary:
        - 10f07d
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrows up down
  search:
    terms: []
  styles:
    - solid
  unicode: f07d
  voted: false
arrows-up-down-left-right:
  aliases:
    names:
      - arrows
    unicodes:
      secondary:
        - 10f047
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Arrows up down left right
  search:
    terms: []
  styles:
    - solid
  unicode: f047
  voted: false
arrows-up-to-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Arrows Up-to-line
  search:
    terms: []
  styles:
    - solid
  unicode: e4c2
  voted: false
artstation:
  changes:
    - 5.6.0
    - 5.8.0
  label: Artstation
  search:
    terms: []
  styles:
    - brands
  unicode: f77a
  voted: true
asterisk:
  aliases:
    unicodes:
      composite:
        - '2731'
        - f069
      primary:
        - f069
      secondary:
        - 102a
        - 10f069
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: asterisk
  search:
    terms: []
  styles:
    - solid
  unicode: 2a
  voted: false
asymmetrik:
  changes:
    - 5.0.0
  label: 'Asymmetrik, Ltd.'
  search:
    terms: []
  styles:
    - brands
  unicode: f372
  voted: false
at:
  aliases:
    unicodes:
      composite:
        - f1fa
      primary:
        - f1fa
      secondary:
        - 10f1fa
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: At
  search:
    terms: []
  styles:
    - solid
  unicode: '40'
  voted: false
atlassian:
  changes:
    - 5.6.0
  label: Atlassian
  search:
    terms: []
  styles:
    - brands
  unicode: f77b
  voted: true
atom:
  aliases:
    unicodes:
      composite:
        - 269b
      secondary:
        - 10f5d2
  changes:
    - 5.2.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Atom
  search:
    terms: []
  styles:
    - solid
  unicode: f5d2
  voted: false
audible:
  changes:
    - 5.0.0
  label: Audible
  search:
    terms: []
  styles:
    - brands
  unicode: f373
  voted: false
audio-description:
  aliases:
    unicodes:
      secondary:
        - 10f29e
  changes:
    - 4.6.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Rectangle audio description
  search:
    terms: []
  styles:
    - solid
  unicode: f29e
  voted: false
austral-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Austral Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e0a9
  voted: false
autoprefixer:
  changes:
    - 5.0.0
  label: Autoprefixer
  search:
    terms: []
  styles:
    - brands
  unicode: f41c
  voted: false
avianex:
  changes:
    - 5.0.0
  label: avianex
  search:
    terms: []
  styles:
    - brands
  unicode: f374
  voted: false
aviato:
  changes:
    - 5.0.0
  label: Aviato
  search:
    terms: []
  styles:
    - brands
  unicode: f421
  voted: false
award:
  aliases:
    unicodes:
      secondary:
        - 10f559
  changes:
    - 5.1.0
    - 5.2.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Award
  search:
    terms: []
  styles:
    - solid
  unicode: f559
  voted: true
aws:
  changes:
    - 5.0.0
    - 5.1.0
  label: Amazon Web Services (AWS)
  search:
    terms: []
  styles:
    - brands
  unicode: f375
  voted: false
b:
  aliases:
    unicodes:
      composite:
        - '62'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: B
  search:
    terms: []
  styles:
    - solid
  unicode: '42'
  voted: false
baby:
  aliases:
    unicodes:
      secondary:
        - 10f77c
  changes:
    - 5.6.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Baby
  search:
    terms: []
  styles:
    - solid
  unicode: f77c
  voted: true
baby-carriage:
  aliases:
    names:
      - carriage-baby
    unicodes:
      secondary:
        - 10f77d
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Baby Carriage
  search:
    terms: []
  styles:
    - solid
  unicode: f77d
  voted: true
backward:
  aliases:
    unicodes:
      composite:
        - 23ea
      secondary:
        - 10f04a
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: backward
  search:
    terms: []
  styles:
    - solid
  unicode: f04a
  voted: false
backward-fast:
  aliases:
    names:
      - fast-backward
    unicodes:
      composite:
        - 23ee
      secondary:
        - 10f049
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Backward fast
  search:
    terms: []
  styles:
    - solid
  unicode: f049
  voted: false
backward-step:
  aliases:
    names:
      - step-backward
    unicodes:
      secondary:
        - 10f048
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Backward step
  search:
    terms: []
  styles:
    - solid
  unicode: f048
  voted: false
bacon:
  aliases:
    unicodes:
      composite:
        - 1f953
      secondary:
        - 10f7e5
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bacon
  search:
    terms: []
  styles:
    - solid
  unicode: f7e5
  voted: false
bacteria:
  aliases:
    unicodes:
      secondary:
        - '10e059'
  changes:
    - 5.13.0
    - 5.13.1
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bacteria
  search:
    terms: []
  styles:
    - solid
  unicode: e059
  voted: false
bacterium:
  aliases:
    unicodes:
      secondary:
        - 10e05a
  changes:
    - 5.13.0
    - 5.13.1
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bacterium
  search:
    terms: []
  styles:
    - solid
  unicode: e05a
  voted: false
bag-shopping:
  aliases:
    names:
      - shopping-bag
    unicodes:
      secondary:
        - 10f290
  changes:
    - 4.5.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Bag shopping
  search:
    terms: []
  styles:
    - solid
  unicode: f290
  voted: false
bahai:
  aliases:
    names:
      - haykal
    unicodes:
      secondary:
        - 10f666
  changes:
    - 5.3.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bahá'í
  search:
    terms: []
  styles:
    - solid
  unicode: f666
  voted: false
baht-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Baht Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e0ac
  voted: false
ban:
  aliases:
    names:
      - cancel
    unicodes:
      composite:
        - 1f6ab
      secondary:
        - 10f05e
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: ban
  search:
    terms: []
  styles:
    - solid
  unicode: f05e
  voted: false
ban-smoking:
  aliases:
    names:
      - smoking-ban
    unicodes:
      composite:
        - 1f6ad
      secondary:
        - 10f54d
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Ban smoking
  search:
    terms: []
  styles:
    - solid
  unicode: f54d
  voted: true
bandage:
  aliases:
    names:
      - band-aid
    unicodes:
      composite:
        - 1fa79
      secondary:
        - 10f462
  changes:
    - 5.0.7
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Bandage
  search:
    terms: []
  styles:
    - solid
  unicode: f462
  voted: false
bandcamp:
  changes:
    - 4.7.0
    - 5.0.0
    - 5.13.1
  label: Bandcamp
  search:
    terms: []
  styles:
    - brands
  unicode: f2d5
  voted: false
barcode:
  aliases:
    unicodes:
      secondary:
        - 10f02a
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: barcode
  search:
    terms: []
  styles:
    - solid
  unicode: f02a
  voted: false
bars:
  aliases:
    names:
      - navicon
    unicodes:
      secondary:
        - 10f0c9
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Bars
  search:
    terms: []
  styles:
    - solid
  unicode: f0c9
  voted: false
bars-progress:
  aliases:
    names:
      - tasks-alt
    unicodes:
      secondary:
        - 10f828
  changes:
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Bars progress
  search:
    terms: []
  styles:
    - solid
  unicode: f828
  voted: false
bars-staggered:
  aliases:
    names:
      - reorder
      - stream
    unicodes:
      secondary:
        - 10f550
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Bars staggered
  search:
    terms: []
  styles:
    - solid
  unicode: f550
  voted: false
baseball:
  aliases:
    names:
      - baseball-ball
    unicodes:
      composite:
        - 1f94e
        - 26be
      secondary:
        - 10f433
  changes:
    - 5.0.5
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Baseball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f433
  voted: false
baseball-bat-ball:
  aliases:
    unicodes:
      secondary:
        - 10f432
  changes:
    - 5.0.5
    - 6.0.0-beta1
    - 6.2.0
  label: Baseball bat ball
  search:
    terms: []
  styles:
    - solid
  unicode: f432
  voted: false
basket-shopping:
  aliases:
    names:
      - shopping-basket
    unicodes:
      secondary:
        - 10f291
  changes:
    - 4.5.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Basket shopping
  search:
    terms: []
  styles:
    - solid
  unicode: f291
  voted: false
basketball:
  aliases:
    names:
      - basketball-ball
    unicodes:
      composite:
        - 1f3c0
      secondary:
        - 10f434
  changes:
    - 5.0.5
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Basketball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f434
  voted: false
bath:
  aliases:
    names:
      - bathtub
    unicodes:
      composite:
        - 1f6c1
      secondary:
        - 10f2cd
  changes:
    - 4.7.0
    - 5.0.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bath
  search:
    terms: []
  styles:
    - solid
  unicode: f2cd
  voted: false
battery-empty:
  aliases:
    names:
      - battery-0
    unicodes:
      secondary:
        - 10f244
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Battery Empty
  search:
    terms: []
  styles:
    - solid
  unicode: f244
  voted: false
battery-full:
  aliases:
    names:
      - battery
      - battery-5
    unicodes:
      composite:
        - 1f50b
      secondary:
        - 10f240
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Battery Full
  search:
    terms: []
  styles:
    - solid
  unicode: f240
  voted: false
battery-half:
  aliases:
    names:
      - battery-3
    unicodes:
      secondary:
        - 10f242
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Battery 1/2 Full
  search:
    terms: []
  styles:
    - solid
  unicode: f242
  voted: false
battery-quarter:
  aliases:
    names:
      - battery-2
    unicodes:
      secondary:
        - 10f243
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Battery 1/4 Full
  search:
    terms: []
  styles:
    - solid
  unicode: f243
  voted: false
battery-three-quarters:
  aliases:
    names:
      - battery-4
    unicodes:
      secondary:
        - 10f241
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Battery 3/4 Full
  search:
    terms: []
  styles:
    - solid
  unicode: f241
  voted: false
battle-net:
  changes:
    - 5.8.0
  label: Battle.net
  search:
    terms: []
  styles:
    - brands
  unicode: f835
  voted: false
bed:
  aliases:
    unicodes:
      composite:
        - 1f6cc
      secondary:
        - 10f236
  changes:
    - 4.3.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Bed
  search:
    terms: []
  styles:
    - solid
  unicode: f236
  voted: false
bed-pulse:
  aliases:
    names:
      - procedures
    unicodes:
      secondary:
        - 10f487
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Bed pulse
  search:
    terms: []
  styles:
    - solid
  unicode: f487
  voted: false
beer-mug-empty:
  aliases:
    names:
      - beer
    unicodes:
      secondary:
        - 10f0fc
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Beer mug empty
  search:
    terms: []
  styles:
    - solid
  unicode: f0fc
  voted: false
behance:
  changes:
    - 4.1.0
    - 5.0.0
  label: Behance
  search:
    terms: []
  styles:
    - brands
  unicode: f1b4
  voted: false
bell:
  aliases:
    unicodes:
      composite:
        - 1f514
        - f0a2
      secondary:
        - 10f0f3
  changes:
    - 2.0.0
    - 5.0.0
    - 5.2.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: bell
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0f3
  voted: false
bell-concierge:
  aliases:
    names:
      - concierge-bell
    unicodes:
      composite:
        - 1f6ce
      secondary:
        - 10f562
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bell concierge
  search:
    terms: []
  styles:
    - solid
  unicode: f562
  voted: false
bell-slash:
  aliases:
    unicodes:
      composite:
        - 1f515
        - f1f7
      secondary:
        - 10f1f6
  changes:
    - 4.2.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Bell Slash
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1f6
  voted: false
bezier-curve:
  aliases:
    unicodes:
      secondary:
        - 10f55b
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Bezier Curve
  search:
    terms: []
  styles:
    - solid
  unicode: f55b
  voted: false
bicycle:
  aliases:
    unicodes:
      composite:
        - 1f6b2
      secondary:
        - 10f206
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Bicycle
  search:
    terms: []
  styles:
    - solid
  unicode: f206
  voted: false
bilibili:
  changes:
    - 6.0.0-beta2
  label: Bilibili
  search:
    terms: []
  styles:
    - brands
  unicode: e3d9
  voted: true
bimobject:
  changes:
    - 5.0.0
  label: BIMobject
  search:
    terms: []
  styles:
    - brands
  unicode: f378
  voted: false
binoculars:
  aliases:
    unicodes:
      secondary:
        - 10f1e5
  changes:
    - 4.2.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Binoculars
  search:
    terms: []
  styles:
    - solid
  unicode: f1e5
  voted: false
biohazard:
  aliases:
    unicodes:
      composite:
        - '2623'
      secondary:
        - 10f780
  changes:
    - 5.6.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Biohazard
  search:
    terms: []
  styles:
    - solid
  unicode: f780
  voted: true
bitbucket:
  aliases:
    unicodes:
      composite:
        - f172
  changes:
    - 3.2.0
    - 5.0.0
    - 5.6.0
    - 5.8.0
  label: Bitbucket
  search:
    terms: []
  styles:
    - brands
  unicode: f171
  voted: false
bitcoin:
  changes:
    - 5.0.0
  label: Bitcoin
  search:
    terms: []
  styles:
    - brands
  unicode: f379
  voted: false
bitcoin-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Bitcoin Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e0b4
  voted: false
bity:
  changes:
    - 5.0.0
  label: Bity
  search:
    terms: []
  styles:
    - brands
  unicode: f37a
  voted: false
black-tie:
  changes:
    - 4.4.0
    - 5.0.0
  label: Font Awesome Black Tie
  search:
    terms: []
  styles:
    - brands
  unicode: f27e
  voted: false
blackberry:
  changes:
    - 5.0.0
  label: BlackBerry
  search:
    terms: []
  styles:
    - brands
  unicode: f37b
  voted: false
blender:
  aliases:
    unicodes:
      secondary:
        - 10f517
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Blender
  search:
    terms: []
  styles:
    - solid
  unicode: f517
  voted: false
blender-phone:
  aliases:
    unicodes:
      secondary:
        - 10f6b6
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Blender Phone
  search:
    terms: []
  styles:
    - solid
  unicode: f6b6
  voted: false
blog:
  aliases:
    unicodes:
      secondary:
        - 10f781
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Blog
  search:
    terms: []
  styles:
    - solid
  unicode: f781
  voted: true
blogger:
  changes:
    - 5.0.0
  label: Blogger
  search:
    terms: []
  styles:
    - brands
  unicode: f37c
  voted: false
blogger-b:
  changes:
    - 5.0.0
  label: Blogger B
  search:
    terms: []
  styles:
    - brands
  unicode: f37d
  voted: false
bluetooth:
  aliases:
    unicodes:
      secondary:
        - 10f293
  changes:
    - 4.5.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bluetooth
  search:
    terms: []
  styles:
    - brands
  unicode: f293
  voted: false
bluetooth-b:
  changes:
    - 4.5.0
    - 5.0.0
  label: Bluetooth
  search:
    terms: []
  styles:
    - brands
  unicode: f294
  voted: false
bold:
  aliases:
    unicodes:
      secondary:
        - 10f032
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: bold
  search:
    terms: []
  styles:
    - solid
  unicode: f032
  voted: false
bolt:
  aliases:
    names:
      - zap
    unicodes:
      composite:
        - 26a1
      secondary:
        - 10f0e7
  changes:
    - 2.0.0
    - 5.0.0
    - 5.5.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Bolt
  search:
    terms: []
  styles:
    - solid
  unicode: f0e7
  voted: false
bolt-lightning:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Lightning Bolt
  search:
    terms: []
  styles:
    - solid
  unicode: e0b7
  voted: false
bomb:
  aliases:
    unicodes:
      composite:
        - 1f4a3
      secondary:
        - 10f1e2
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.1.2
    - 6.2.0
  label: Bomb
  search:
    terms: []
  styles:
    - solid
  unicode: f1e2
  voted: false
bone:
  aliases:
    unicodes:
      composite:
        - 1f9b4
      secondary:
        - 10f5d7
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bone
  search:
    terms: []
  styles:
    - solid
  unicode: f5d7
  voted: false
bong:
  aliases:
    unicodes:
      secondary:
        - 10f55c
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bong
  search:
    terms: []
  styles:
    - solid
  unicode: f55c
  voted: false
book:
  aliases:
    unicodes:
      composite:
        - 1f4d4
      secondary:
        - 10f02d
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: book
  search:
    terms: []
  styles:
    - solid
  unicode: f02d
  voted: false
book-atlas:
  aliases:
    names:
      - atlas
    unicodes:
      secondary:
        - 10f558
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book atlas
  search:
    terms: []
  styles:
    - solid
  unicode: f558
  voted: false
book-bible:
  aliases:
    names:
      - bible
    unicodes:
      secondary:
        - 10f647
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book bible
  search:
    terms: []
  styles:
    - solid
  unicode: f647
  voted: false
book-bookmark:
  changes:
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Book Bookmark
  search:
    terms: []
  styles:
    - solid
  unicode: e0bb
  voted: false
book-journal-whills:
  aliases:
    names:
      - journal-whills
    unicodes:
      secondary:
        - 10f66a
  changes:
    - 5.3.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book journal whills
  search:
    terms: []
  styles:
    - solid
  unicode: f66a
  voted: false
book-medical:
  aliases:
    unicodes:
      secondary:
        - 10f7e6
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Medical Book
  search:
    terms: []
  styles:
    - solid
  unicode: f7e6
  voted: false
book-open:
  aliases:
    unicodes:
      composite:
        - 1f4d6
        - 1f56e
      secondary:
        - 10f518
  changes:
    - 5.0.13
    - 5.1.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book Open
  search:
    terms: []
  styles:
    - solid
  unicode: f518
  voted: true
book-open-reader:
  aliases:
    names:
      - book-reader
    unicodes:
      secondary:
        - 10f5da
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book open reader
  search:
    terms: []
  styles:
    - solid
  unicode: f5da
  voted: false
book-quran:
  aliases:
    names:
      - quran
    unicodes:
      secondary:
        - 10f687
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book quran
  search:
    terms: []
  styles:
    - solid
  unicode: f687
  voted: false
book-skull:
  aliases:
    names:
      - book-dead
    unicodes:
      secondary:
        - 10f6b7
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book skull
  search:
    terms: []
  styles:
    - solid
  unicode: f6b7
  voted: false
book-tanakh:
  aliases:
    names:
      - tanakh
    unicodes:
      secondary:
        - 10f827
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Book tanakh
  search:
    terms: []
  styles:
    - solid
  unicode: f827
  voted: false
bookmark:
  aliases:
    unicodes:
      composite:
        - 1f516
        - f097
      secondary:
        - 10f02e
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: bookmark
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f02e
  voted: false
bootstrap:
  changes:
    - 5.8.0
    - 5.15.4
    - 6.0.0-beta1
  label: Bootstrap
  search:
    terms: []
  styles:
    - brands
  unicode: f836
  voted: false
border-all:
  aliases:
    unicodes:
      secondary:
        - 10f84c
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Border All
  search:
    terms: []
  styles:
    - solid
  unicode: f84c
  voted: false
border-none:
  aliases:
    unicodes:
      secondary:
        - 10f850
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Border None
  search:
    terms: []
  styles:
    - solid
  unicode: f850
  voted: false
border-top-left:
  aliases:
    names:
      - border-style
    unicodes:
      secondary:
        - 10f853
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Border top left
  search:
    terms: []
  styles:
    - solid
  unicode: f853
  voted: false
bore-hole:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bore Hole
  search:
    terms: []
  styles:
    - solid
  unicode: e4c3
  voted: false
bots:
  changes:
    - 6.0.0-beta1
  label: Bots
  search:
    terms: []
  styles:
    - brands
  unicode: e340
  voted: false
bottle-droplet:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bottle Droplet
  search:
    terms: []
  styles:
    - solid
  unicode: e4c4
  voted: false
bottle-water:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bottle Water
  search:
    terms: []
  styles:
    - solid
  unicode: e4c5
  voted: false
bowl-food:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Bowl Food
  search:
    terms: []
  styles:
    - solid
  unicode: e4c6
  voted: false
bowl-rice:
  changes:
    - 6.0.0-beta1
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Bowl Rice
  search:
    terms: []
  styles:
    - solid
  unicode: e2eb
  voted: false
bowling-ball:
  aliases:
    unicodes:
      secondary:
        - 10f436
  changes:
    - 5.0.5
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Bowling Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f436
  voted: false
box:
  aliases:
    unicodes:
      composite:
        - 1f4e6
      secondary:
        - 10f466
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Box
  search:
    terms: []
  styles:
    - solid
  unicode: f466
  voted: false
box-archive:
  aliases:
    names:
      - archive
    unicodes:
      secondary:
        - 10f187
  changes:
    - 3.2.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Box archive
  search:
    terms: []
  styles:
    - solid
  unicode: f187
  voted: false
box-open:
  aliases:
    unicodes:
      secondary:
        - 10f49e
  changes:
    - 5.0.9
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Box Open
  search:
    terms: []
  styles:
    - solid
  unicode: f49e
  voted: false
box-tissue:
  aliases:
    unicodes:
      secondary:
        - 10e05b
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Tissue Box
  search:
    terms: []
  styles:
    - solid
  unicode: e05b
  voted: false
boxes-packing:
  changes:
    - 6.1.0
    - 6.2.0
  label: Boxes Packing
  search:
    terms: []
  styles:
    - solid
  unicode: e4c7
  voted: false
boxes-stacked:
  aliases:
    names:
      - boxes
      - boxes-alt
    unicodes:
      composite:
        - f4a1
      primary:
        - f4a1
      secondary:
        - 10f468
        - 10f4a1
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Boxes stacked
  search:
    terms: []
  styles:
    - solid
  unicode: f468
  voted: false
braille:
  aliases:
    unicodes:
      secondary:
        - 10f2a1
  changes:
    - 4.6.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Braille
  search:
    terms: []
  styles:
    - solid
  unicode: f2a1
  voted: false
brain:
  aliases:
    unicodes:
      composite:
        - 1f9e0
      secondary:
        - 10f5dc
  changes:
    - 5.2.0
    - 5.9.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Brain
  search:
    terms: []
  styles:
    - solid
  unicode: f5dc
  voted: false
brazilian-real-sign:
  changes:
    - 6.0.0-beta3
    - 6.2.0
  label: Brazilian Real Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e46c
  voted: false
bread-slice:
  aliases:
    unicodes:
      secondary:
        - 10f7ec
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Bread Slice
  search:
    terms: []
  styles:
    - solid
  unicode: f7ec
  voted: false
bridge:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bridge
  search:
    terms: []
  styles:
    - solid
  unicode: e4c8
  voted: false
bridge-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bridge Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e4c9
  voted: false
bridge-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bridge Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e4ca
  voted: false
bridge-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bridge Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e4cb
  voted: false
bridge-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bridge Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e4cc
  voted: false
bridge-water:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bridge Water
  search:
    terms: []
  styles:
    - solid
  unicode: e4ce
  voted: false
briefcase:
  aliases:
    unicodes:
      composite:
        - 1f4bc
      secondary:
        - 10f0b1
  changes:
    - 2.0.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Briefcase
  search:
    terms: []
  styles:
    - solid
  unicode: f0b1
  voted: false
briefcase-medical:
  aliases:
    unicodes:
      secondary:
        - 10f469
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Medical Briefcase
  search:
    terms: []
  styles:
    - solid
  unicode: f469
  voted: false
broom:
  aliases:
    unicodes:
      composite:
        - 1f9f9
      secondary:
        - 10f51a
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Broom
  search:
    terms: []
  styles:
    - solid
  unicode: f51a
  voted: true
broom-ball:
  aliases:
    names:
      - quidditch
      - quidditch-broom-ball
    unicodes:
      secondary:
        - 10f458
  changes:
    - 5.0.5
    - 6.0.0-beta1
    - 6.2.0
  label: Broom and Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f458
  voted: false
brush:
  aliases:
    unicodes:
      secondary:
        - 10f55d
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Brush
  search:
    terms: []
  styles:
    - solid
  unicode: f55d
  voted: false
btc:
  changes:
    - 3.2.0
    - 5.0.0
  label: BTC
  search:
    terms: []
  styles:
    - brands
  unicode: f15a
  voted: false
bucket:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bucket
  search:
    terms: []
  styles:
    - solid
  unicode: e4cf
  voted: false
buffer:
  changes:
    - 5.8.0
  label: Buffer
  search:
    terms: []
  styles:
    - brands
  unicode: f837
  voted: false
bug:
  aliases:
    unicodes:
      secondary:
        - 10f188
  changes:
    - 3.2.0
    - 5.0.0
    - 5.15.4
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Bug
  search:
    terms: []
  styles:
    - solid
  unicode: f188
  voted: false
bug-slash:
  changes:
    - 6.0.0
    - 6.2.0
  label: Bug Slash
  search:
    terms: []
  styles:
    - solid
  unicode: e490
  voted: false
bugs:
  changes:
    - 6.1.0
    - 6.2.0
  label: Bugs
  search:
    terms: []
  styles:
    - solid
  unicode: e4d0
  voted: false
building:
  aliases:
    unicodes:
      composite:
        - 1f3e2
        - f0f7
      secondary:
        - 10f1ad
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Building
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1ad
  voted: false
building-circle-arrow-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Circle-arrow-right
  search:
    terms: []
  styles:
    - solid
  unicode: e4d1
  voted: false
building-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e4d2
  voted: false
building-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e4d3
  voted: false
building-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e4d4
  voted: false
building-columns:
  aliases:
    names:
      - bank
      - institution
      - museum
      - university
    unicodes:
      secondary:
        - 10f19c
  changes:
    - 4.1.0
    - 5.0.0
    - 5.0.3
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Building with Columns
  search:
    terms: []
  styles:
    - solid
  unicode: f19c
  voted: false
building-flag:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Flag
  search:
    terms: []
  styles:
    - solid
  unicode: e4d5
  voted: false
building-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e4d6
  voted: false
building-ngo:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Ngo
  search:
    terms: []
  styles:
    - solid
  unicode: e4d7
  voted: false
building-shield:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Shield
  search:
    terms: []
  styles:
    - solid
  unicode: e4d8
  voted: false
building-un:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Un
  search:
    terms: []
  styles:
    - solid
  unicode: e4d9
  voted: false
building-user:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building User
  search:
    terms: []
  styles:
    - solid
  unicode: e4da
  voted: false
building-wheat:
  changes:
    - 6.1.0
    - 6.2.0
  label: Building Wheat
  search:
    terms: []
  styles:
    - solid
  unicode: e4db
  voted: false
bullhorn:
  aliases:
    unicodes:
      composite:
        - 1f4e2
        - 1f56b
      secondary:
        - 10f0a1
  changes:
    - 2.0.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: bullhorn
  search:
    terms: []
  styles:
    - solid
  unicode: f0a1
  voted: false
bullseye:
  aliases:
    unicodes:
      secondary:
        - 10f140
  changes:
    - 3.1.0
    - 5.0.0
    - 5.3.0
    - 5.10.1
    - 5.15.4
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Bullseye
  search:
    terms: []
  styles:
    - solid
  unicode: f140
  voted: false
burger:
  aliases:
    names:
      - hamburger
    unicodes:
      secondary:
        - 10f805
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Burger
  search:
    terms: []
  styles:
    - solid
  unicode: f805
  voted: false
buromobelexperte:
  changes:
    - 5.0.0
  label: Büromöbel-Experte GmbH & Co. KG.
  search:
    terms: []
  styles:
    - brands
  unicode: f37f
  voted: false
burst:
  changes:
    - 6.1.0
    - 6.2.0
  label: Burst
  search:
    terms: []
  styles:
    - solid
  unicode: e4dc
  voted: false
bus:
  aliases:
    unicodes:
      composite:
        - 1f68d
      secondary:
        - 10f207
  changes:
    - 4.2.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Bus
  search:
    terms: []
  styles:
    - solid
  unicode: f207
  voted: false
bus-simple:
  aliases:
    names:
      - bus-alt
    unicodes:
      secondary:
        - 10f55e
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Bus simple
  search:
    terms: []
  styles:
    - solid
  unicode: f55e
  voted: false
business-time:
  aliases:
    names:
      - briefcase-clock
    unicodes:
      secondary:
        - 10f64a
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Briefcase clock
  search:
    terms: []
  styles:
    - solid
  unicode: f64a
  voted: false
buy-n-large:
  changes:
    - 5.11.0
  label: Buy n Large
  search:
    terms: []
  styles:
    - brands
  unicode: f8a6
  voted: false
buysellads:
  changes:
    - 4.3.0
    - 5.0.0
  label: BuySellAds
  search:
    terms: []
  styles:
    - brands
  unicode: f20d
  voted: false
c:
  aliases:
    unicodes:
      composite:
        - '63'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: C
  search:
    terms: []
  styles:
    - solid
  unicode: '43'
  voted: false
cable-car:
  aliases:
    names:
      - tram
    unicodes:
      composite:
        - 1f6a1
        - e0cf
      secondary:
        - 10f7da
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.2
    - 6.2.0
  label: Cable Car
  search:
    terms: []
  styles:
    - solid
  unicode: f7da
  voted: false
cake-candles:
  aliases:
    names:
      - birthday-cake
      - cake
    unicodes:
      composite:
        - 1f382
      secondary:
        - 10f1fd
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cake candles
  search:
    terms: []
  styles:
    - solid
  unicode: f1fd
  voted: false
calculator:
  aliases:
    unicodes:
      composite:
        - 1f5a9
      secondary:
        - 10f1ec
  changes:
    - 4.2.0
    - 5.0.0
    - 5.3.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Calculator
  search:
    terms: []
  styles:
    - solid
  unicode: f1ec
  voted: false
calendar:
  aliases:
    unicodes:
      composite:
        - 1f4c5
        - 1f4c6
      secondary:
        - 10f133
  changes:
    - 3.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f133
  voted: false
calendar-check:
  aliases:
    unicodes:
      secondary:
        - 10f274
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar Check
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f274
  voted: false
calendar-day:
  aliases:
    unicodes:
      secondary:
        - 10f783
  changes:
    - 5.6.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar with Day Focus
  search:
    terms: []
  styles:
    - solid
  unicode: f783
  voted: true
calendar-days:
  aliases:
    names:
      - calendar-alt
    unicodes:
      secondary:
        - 10f073
  changes:
    - 1.0.0
    - 5.0.0
    - 5.6.0
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar Days
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f073
  voted: false
calendar-minus:
  aliases:
    unicodes:
      secondary:
        - 10f272
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar Minus
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f272
  voted: false
calendar-plus:
  aliases:
    unicodes:
      secondary:
        - 10f271
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar Plus
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f271
  voted: false
calendar-week:
  aliases:
    unicodes:
      secondary:
        - 10f784
  changes:
    - 5.6.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar with Week Focus
  search:
    terms: []
  styles:
    - solid
  unicode: f784
  voted: true
calendar-xmark:
  aliases:
    names:
      - calendar-times
    unicodes:
      secondary:
        - 10f273
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Calendar X Mark
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f273
  voted: false
camera:
  aliases:
    names:
      - camera-alt
    unicodes:
      composite:
        - f332
      primary:
        - f332
      secondary:
        - 10f030
        - 10f332
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: camera
  search:
    terms: []
  styles:
    - solid
  unicode: f030
  voted: false
camera-retro:
  aliases:
    unicodes:
      composite:
        - 1f4f7
      secondary:
        - 10f083
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Retro Camera
  search:
    terms: []
  styles:
    - solid
  unicode: f083
  voted: false
camera-rotate:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Camera Rotate
  search:
    terms: []
  styles:
    - solid
  unicode: e0d8
  voted: true
campground:
  aliases:
    unicodes:
      composite:
        - 26fa
      secondary:
        - 10f6bb
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Campground
  search:
    terms: []
  styles:
    - solid
  unicode: f6bb
  voted: false
canadian-maple-leaf:
  changes:
    - 5.6.0
    - 5.8.0
  label: Canadian Maple Leaf
  search:
    terms: []
  styles:
    - brands
  unicode: f785
  voted: false
candy-cane:
  aliases:
    unicodes:
      secondary:
        - 10f786
  changes:
    - 5.6.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Candy Cane
  search:
    terms: []
  styles:
    - solid
  unicode: f786
  voted: false
cannabis:
  aliases:
    unicodes:
      secondary:
        - 10f55f
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cannabis
  search:
    terms: []
  styles:
    - solid
  unicode: f55f
  voted: false
capsules:
  aliases:
    unicodes:
      secondary:
        - 10f46b
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Capsules
  search:
    terms: []
  styles:
    - solid
  unicode: f46b
  voted: false
car:
  aliases:
    names:
      - automobile
    unicodes:
      composite:
        - 1f698
      secondary:
        - 10f1b9
  changes:
    - 4.1.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Car
  search:
    terms: []
  styles:
    - solid
  unicode: f1b9
  voted: false
car-battery:
  aliases:
    names:
      - battery-car
    unicodes:
      secondary:
        - 10f5df
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Car Battery
  search:
    terms: []
  styles:
    - solid
  unicode: f5df
  voted: false
car-burst:
  aliases:
    names:
      - car-crash
    unicodes:
      secondary:
        - 10f5e1
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Car Crash
  search:
    terms: []
  styles:
    - solid
  unicode: f5e1
  voted: false
car-on:
  changes:
    - 6.1.0
    - 6.2.0
  label: Car On
  search:
    terms: []
  styles:
    - solid
  unicode: e4dd
  voted: false
car-rear:
  aliases:
    names:
      - car-alt
    unicodes:
      secondary:
        - 10f5de
  changes:
    - 5.2.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Car rear
  search:
    terms: []
  styles:
    - solid
  unicode: f5de
  voted: false
car-side:
  aliases:
    unicodes:
      composite:
        - 1f697
      secondary:
        - 10f5e4
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Car Side
  search:
    terms: []
  styles:
    - solid
  unicode: f5e4
  voted: false
car-tunnel:
  changes:
    - 6.1.0
    - 6.2.0
  label: Car Tunnel
  search:
    terms: []
  styles:
    - solid
  unicode: e4de
  voted: false
caravan:
  aliases:
    unicodes:
      secondary:
        - 10f8ff
  changes:
    - 5.12.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Caravan
  search:
    terms: []
  styles:
    - solid
  unicode: f8ff
  voted: true
caret-down:
  aliases:
    unicodes:
      secondary:
        - 10f0d7
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Caret Down
  search:
    terms: []
  styles:
    - solid
  unicode: f0d7
  voted: false
caret-left:
  aliases:
    unicodes:
      secondary:
        - 10f0d9
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Caret Left
  search:
    terms: []
  styles:
    - solid
  unicode: f0d9
  voted: false
caret-right:
  aliases:
    unicodes:
      secondary:
        - 10f0da
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Caret Right
  search:
    terms: []
  styles:
    - solid
  unicode: f0da
  voted: false
caret-up:
  aliases:
    unicodes:
      secondary:
        - 10f0d8
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Caret Up
  search:
    terms: []
  styles:
    - solid
  unicode: f0d8
  voted: false
carrot:
  aliases:
    unicodes:
      composite:
        - 1f955
      secondary:
        - 10f787
  changes:
    - 5.6.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Carrot
  search:
    terms: []
  styles:
    - solid
  unicode: f787
  voted: false
cart-arrow-down:
  aliases:
    unicodes:
      secondary:
        - 10f218
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Shopping Cart Arrow Down
  search:
    terms: []
  styles:
    - solid
  unicode: f218
  voted: false
cart-flatbed:
  aliases:
    names:
      - dolly-flatbed
    unicodes:
      secondary:
        - 10f474
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Cart flatbed
  search:
    terms: []
  styles:
    - solid
  unicode: f474
  voted: false
cart-flatbed-suitcase:
  aliases:
    names:
      - luggage-cart
    unicodes:
      secondary:
        - 10f59d
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cart flatbed suitcase
  search:
    terms: []
  styles:
    - solid
  unicode: f59d
  voted: false
cart-plus:
  aliases:
    unicodes:
      secondary:
        - 10f217
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Add to Shopping Cart
  search:
    terms: []
  styles:
    - solid
  unicode: f217
  voted: false
cart-shopping:
  aliases:
    names:
      - shopping-cart
    unicodes:
      composite:
        - 1f6d2
      secondary:
        - 10f07a
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cart shopping
  search:
    terms: []
  styles:
    - solid
  unicode: f07a
  voted: false
cash-register:
  aliases:
    unicodes:
      secondary:
        - 10f788
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cash Register
  search:
    terms: []
  styles:
    - solid
  unicode: f788
  voted: true
cat:
  aliases:
    unicodes:
      composite:
        - 1f408
      secondary:
        - 10f6be
  changes:
    - 5.4.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Cat
  search:
    terms: []
  styles:
    - solid
  unicode: f6be
  voted: true
cc-amazon-pay:
  changes:
    - 5.0.2
  label: Amazon Pay Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f42d
  voted: false
cc-amex:
  changes:
    - 4.2.0
    - 5.0.0
    - 5.7.0
    - 6.1.2
  label: American Express Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f3
  voted: false
cc-apple-pay:
  changes:
    - 5.0.0
  label: Apple Pay Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f416
  voted: false
cc-diners-club:
  changes:
    - 4.4.0
    - 5.0.0
  label: Diner's Club Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f24c
  voted: false
cc-discover:
  changes:
    - 4.2.0
    - 5.0.0
  label: Discover Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f2
  voted: false
cc-jcb:
  changes:
    - 4.4.0
    - 5.0.0
  label: JCB Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f24b
  voted: false
cc-mastercard:
  changes:
    - 4.2.0
    - 5.0.0
  label: MasterCard Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f1
  voted: false
cc-paypal:
  changes:
    - 4.2.0
    - 5.0.0
  label: Paypal Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f4
  voted: false
cc-stripe:
  changes:
    - 4.2.0
    - 5.0.0
  label: Stripe Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f5
  voted: false
cc-visa:
  changes:
    - 4.2.0
    - 5.0.0
  label: Visa Credit Card
  search:
    terms: []
  styles:
    - brands
  unicode: f1f0
  voted: false
cedi-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cedi Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e0df
  voted: false
cent-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cent Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e3f5
  voted: false
centercode:
  changes:
    - 5.0.0
  label: Centercode
  search:
    terms: []
  styles:
    - brands
  unicode: f380
  voted: false
centos:
  changes:
    - 5.6.0
    - 5.8.0
  label: Centos
  search:
    terms: []
  styles:
    - brands
  unicode: f789
  voted: true
certificate:
  aliases:
    unicodes:
      secondary:
        - 10f0a3
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: certificate
  search:
    terms: []
  styles:
    - solid
  unicode: f0a3
  voted: false
chair:
  aliases:
    unicodes:
      composite:
        - 1fa91
      secondary:
        - 10f6c0
  changes:
    - 5.4.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chair
  search:
    terms: []
  styles:
    - solid
  unicode: f6c0
  voted: true
chalkboard:
  aliases:
    names:
      - blackboard
    unicodes:
      secondary:
        - 10f51b
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Chalkboard
  search:
    terms: []
  styles:
    - solid
  unicode: f51b
  voted: false
chalkboard-user:
  aliases:
    names:
      - chalkboard-teacher
    unicodes:
      secondary:
        - 10f51c
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Chalkboard user
  search:
    terms: []
  styles:
    - solid
  unicode: f51c
  voted: false
champagne-glasses:
  aliases:
    names:
      - glass-cheers
    unicodes:
      composite:
        - 1f942
      secondary:
        - 10f79f
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Champagne glasses
  search:
    terms: []
  styles:
    - solid
  unicode: f79f
  voted: false
charging-station:
  aliases:
    unicodes:
      secondary:
        - 10f5e7
  changes:
    - 5.2.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Charging Station
  search:
    terms: []
  styles:
    - solid
  unicode: f5e7
  voted: false
chart-area:
  aliases:
    names:
      - area-chart
    unicodes:
      secondary:
        - 10f1fe
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Area Chart
  search:
    terms: []
  styles:
    - solid
  unicode: f1fe
  voted: false
chart-bar:
  aliases:
    names:
      - bar-chart
    unicodes:
      secondary:
        - 10f080
  changes:
    - 1.0.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Bar Chart
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f080
  voted: false
chart-column:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Chart Column
  search:
    terms: []
  styles:
    - solid
  unicode: e0e3
  voted: false
chart-gantt:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Chart Gantt
  search:
    terms: []
  styles:
    - solid
  unicode: e0e4
  voted: false
chart-line:
  aliases:
    names:
      - line-chart
    unicodes:
      secondary:
        - 10f201
  changes:
    - 4.2.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Line Chart
  search:
    terms: []
  styles:
    - solid
  unicode: f201
  voted: false
chart-pie:
  aliases:
    names:
      - pie-chart
    unicodes:
      secondary:
        - 10f200
  changes:
    - 4.2.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Pie Chart
  search:
    terms: []
  styles:
    - solid
  unicode: f200
  voted: false
chart-simple:
  changes:
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Chart Simple
  search:
    terms: []
  styles:
    - solid
  unicode: e473
  voted: false
check:
  aliases:
    unicodes:
      composite:
        - '2713'
        - '2714'
      secondary:
        - 10f00c
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Check
  search:
    terms: []
  styles:
    - solid
  unicode: f00c
  voted: false
check-double:
  aliases:
    unicodes:
      secondary:
        - 10f560
  changes:
    - 5.1.0
    - 5.8.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Double Check
  search:
    terms: []
  styles:
    - solid
  unicode: f560
  voted: true
check-to-slot:
  aliases:
    names:
      - vote-yea
    unicodes:
      secondary:
        - 10f772
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Check to Slot
  search:
    terms: []
  styles:
    - solid
  unicode: f772
  voted: false
cheese:
  aliases:
    unicodes:
      secondary:
        - 10f7ef
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cheese
  search:
    terms: []
  styles:
    - solid
  unicode: f7ef
  voted: false
chess:
  aliases:
    unicodes:
      secondary:
        - 10f439
  changes:
    - 5.0.5
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess
  search:
    terms: []
  styles:
    - solid
  unicode: f439
  voted: false
chess-bishop:
  aliases:
    unicodes:
      composite:
        - 265d
      secondary:
        - 10f43a
  changes:
    - 5.0.5
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess Bishop
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f43a
  voted: false
chess-board:
  aliases:
    unicodes:
      secondary:
        - 10f43c
  changes:
    - 5.0.5
    - 5.7.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess Board
  search:
    terms: []
  styles:
    - solid
  unicode: f43c
  voted: false
chess-king:
  aliases:
    unicodes:
      composite:
        - 265a
      secondary:
        - 10f43f
  changes:
    - 5.0.5
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess King
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f43f
  voted: false
chess-knight:
  aliases:
    unicodes:
      composite:
        - 265e
      secondary:
        - 10f441
  changes:
    - 5.0.5
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess Knight
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f441
  voted: false
chess-pawn:
  aliases:
    unicodes:
      composite:
        - 265f
      secondary:
        - 10f443
  changes:
    - 5.0.5
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess Pawn
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f443
  voted: false
chess-queen:
  aliases:
    unicodes:
      composite:
        - 265b
      secondary:
        - 10f445
  changes:
    - 5.0.5
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess Queen
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f445
  voted: false
chess-rook:
  aliases:
    unicodes:
      composite:
        - 265c
      secondary:
        - 10f447
  changes:
    - 5.0.5
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Chess Rook
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f447
  voted: false
chevron-down:
  aliases:
    unicodes:
      secondary:
        - 10f078
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: chevron-down
  search:
    terms: []
  styles:
    - solid
  unicode: f078
  voted: false
chevron-left:
  aliases:
    unicodes:
      composite:
        - '2329'
      secondary:
        - 10f053
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: chevron-left
  search:
    terms: []
  styles:
    - solid
  unicode: f053
  voted: false
chevron-right:
  aliases:
    unicodes:
      composite:
        - 232a
      secondary:
        - 10f054
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: chevron-right
  search:
    terms: []
  styles:
    - solid
  unicode: f054
  voted: false
chevron-up:
  aliases:
    unicodes:
      secondary:
        - 10f077
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: chevron-up
  search:
    terms: []
  styles:
    - solid
  unicode: f077
  voted: false
child:
  aliases:
    unicodes:
      secondary:
        - 10f1ae
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.1.1
    - 6.2.0
  label: Child
  search:
    terms: []
  styles:
    - solid
  unicode: f1ae
  voted: false
child-dress:
  changes:
    - 6.1.1
    - 6.2.0
  label: Child Dress
  search:
    terms: []
  styles:
    - solid
  unicode: e59c
  voted: false
child-reaching:
  changes:
    - 6.1.1
    - 6.2.0
  label: Child Reaching
  search:
    terms: []
  styles:
    - solid
  unicode: e59d
  voted: false
child-rifle:
  changes:
    - 6.1.0
    - 6.2.0
  label: Child Rifle
  search:
    terms: []
  styles:
    - solid
  unicode: e4e0
  voted: false
children:
  changes:
    - 6.1.0
    - 6.1.1
    - 6.2.0
  label: Children
  search:
    terms: []
  styles:
    - solid
  unicode: e4e1
  voted: false
chrome:
  changes:
    - 4.4.0
    - 5.0.0
    - 6.1.2
  label: Chrome
  search:
    terms: []
  styles:
    - brands
  unicode: f268
  voted: false
chromecast:
  changes:
    - 5.8.0
  label: Chromecast
  search:
    terms: []
  styles:
    - brands
  unicode: f838
  voted: false
church:
  aliases:
    unicodes:
      composite:
        - 26ea
      secondary:
        - 10f51d
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Church
  search:
    terms: []
  styles:
    - solid
  unicode: f51d
  voted: true
circle:
  aliases:
    unicodes:
      composite:
        - 1f534
        - 1f535
        - 1f7e0
        - 1f7e1
        - 1f7e2
        - 1f7e3
        - 1f7e4
        - 25cf
        - 26aa
        - 26ab
        - 2b24
        - f10c
        - f1db
      secondary:
        - 10f111
  changes:
    - 3.0.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f111
  voted: false
circle-arrow-down:
  aliases:
    names:
      - arrow-circle-down
    unicodes:
      secondary:
        - 10f0ab
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle arrow down
  search:
    terms: []
  styles:
    - solid
  unicode: f0ab
  voted: false
circle-arrow-left:
  aliases:
    names:
      - arrow-circle-left
    unicodes:
      secondary:
        - 10f0a8
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle arrow left
  search:
    terms: []
  styles:
    - solid
  unicode: f0a8
  voted: false
circle-arrow-right:
  aliases:
    names:
      - arrow-circle-right
    unicodes:
      secondary:
        - 10f0a9
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle arrow right
  search:
    terms: []
  styles:
    - solid
  unicode: f0a9
  voted: false
circle-arrow-up:
  aliases:
    names:
      - arrow-circle-up
    unicodes:
      secondary:
        - 10f0aa
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle arrow up
  search:
    terms: []
  styles:
    - solid
  unicode: f0aa
  voted: false
circle-check:
  aliases:
    names:
      - check-circle
    unicodes:
      composite:
        - f05d
      secondary:
        - 10f058
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle check
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f058
  voted: false
circle-chevron-down:
  aliases:
    names:
      - chevron-circle-down
    unicodes:
      secondary:
        - 10f13a
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle chevron down
  search:
    terms: []
  styles:
    - solid
  unicode: f13a
  voted: false
circle-chevron-left:
  aliases:
    names:
      - chevron-circle-left
    unicodes:
      secondary:
        - 10f137
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle chevron left
  search:
    terms: []
  styles:
    - solid
  unicode: f137
  voted: false
circle-chevron-right:
  aliases:
    names:
      - chevron-circle-right
    unicodes:
      secondary:
        - 10f138
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle chevron right
  search:
    terms: []
  styles:
    - solid
  unicode: f138
  voted: false
circle-chevron-up:
  aliases:
    names:
      - chevron-circle-up
    unicodes:
      secondary:
        - 10f139
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle chevron up
  search:
    terms: []
  styles:
    - solid
  unicode: f139
  voted: false
circle-dollar-to-slot:
  aliases:
    names:
      - donate
    unicodes:
      secondary:
        - 10f4b9
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Circle dollar to slot
  search:
    terms: []
  styles:
    - solid
  unicode: f4b9
  voted: false
circle-dot:
  aliases:
    names:
      - dot-circle
    unicodes:
      composite:
        - 1f518
      secondary:
        - 10f192
  changes:
    - 4.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle dot
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f192
  voted: false
circle-down:
  aliases:
    names:
      - arrow-alt-circle-down
    unicodes:
      composite:
        - f01a
      secondary:
        - 10f358
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle down
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f358
  voted: false
circle-exclamation:
  aliases:
    names:
      - exclamation-circle
    unicodes:
      secondary:
        - 10f06a
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: f06a
  voted: false
circle-h:
  aliases:
    names:
      - hospital-symbol
    unicodes:
      composite:
        - 24bd
      secondary:
        - 10f47e
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Circle h
  search:
    terms: []
  styles:
    - solid
  unicode: f47e
  voted: false
circle-half-stroke:
  aliases:
    names:
      - adjust
    unicodes:
      composite:
        - 25d0
      secondary:
        - 10f042
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 5.11.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle half stroke
  search:
    terms: []
  styles:
    - solid
  unicode: f042
  voted: false
circle-info:
  aliases:
    names:
      - info-circle
    unicodes:
      secondary:
        - 10f05a
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle info
  search:
    terms: []
  styles:
    - solid
  unicode: f05a
  voted: false
circle-left:
  aliases:
    names:
      - arrow-alt-circle-left
    unicodes:
      composite:
        - f190
      secondary:
        - 10f359
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle left
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f359
  voted: false
circle-minus:
  aliases:
    names:
      - minus-circle
    unicodes:
      secondary:
        - 10f056
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle minus
  search:
    terms: []
  styles:
    - solid
  unicode: f056
  voted: false
circle-nodes:
  changes:
    - 6.1.0
    - 6.2.0
  label: Circle Nodes
  search:
    terms: []
  styles:
    - solid
  unicode: e4e2
  voted: false
circle-notch:
  aliases:
    unicodes:
      secondary:
        - 10f1ce
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle Notched
  search:
    terms: []
  styles:
    - solid
  unicode: f1ce
  voted: false
circle-pause:
  aliases:
    names:
      - pause-circle
    unicodes:
      composite:
        - f28c
      secondary:
        - 10f28b
  changes:
    - 4.5.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle pause
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f28b
  voted: false
circle-play:
  aliases:
    names:
      - play-circle
    unicodes:
      composite:
        - f01d
      secondary:
        - 10f144
  changes:
    - 3.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle play
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f144
  voted: false
circle-plus:
  aliases:
    names:
      - plus-circle
    unicodes:
      secondary:
        - 10f055
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle plus
  search:
    terms: []
  styles:
    - solid
  unicode: f055
  voted: false
circle-question:
  aliases:
    names:
      - question-circle
    unicodes:
      composite:
        - f29c
      secondary:
        - 10f059
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle question
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f059
  voted: false
circle-radiation:
  aliases:
    names:
      - radiation-alt
    unicodes:
      composite:
        - '2622'
      secondary:
        - 10f7ba
  changes:
    - 5.6.0
    - 5.8.2
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Circle radiation
  search:
    terms: []
  styles:
    - solid
  unicode: f7ba
  voted: true
circle-right:
  aliases:
    names:
      - arrow-alt-circle-right
    unicodes:
      composite:
        - f18e
      secondary:
        - 10f35a
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle right
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f35a
  voted: false
circle-stop:
  aliases:
    names:
      - stop-circle
    unicodes:
      composite:
        - f28e
      secondary:
        - 10f28d
  changes:
    - 4.5.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle stop
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f28d
  voted: false
circle-up:
  aliases:
    names:
      - arrow-alt-circle-up
    unicodes:
      composite:
        - f01b
      secondary:
        - 10f35b
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Circle up
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f35b
  voted: false
circle-user:
  aliases:
    names:
      - user-circle
    unicodes:
      composite:
        - f2be
      secondary:
        - 10f2bd
  changes:
    - 4.7.0
    - 5.0.0
    - 5.0.3
    - 5.0.11
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Circle user
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2bd
  voted: false
circle-xmark:
  aliases:
    names:
      - times-circle
      - xmark-circle
    unicodes:
      composite:
        - f05c
      secondary:
        - 10f057
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Circle X Mark
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f057
  voted: false
city:
  aliases:
    unicodes:
      composite:
        - 1f3d9
      secondary:
        - 10f64f
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: City
  search:
    terms: []
  styles:
    - solid
  unicode: f64f
  voted: false
clapperboard:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Clapperboard
  search:
    terms: []
  styles:
    - solid
  unicode: e131
  voted: true
clipboard:
  aliases:
    unicodes:
      composite:
        - 1f4cb
      secondary:
        - 10f328
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Clipboard
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f328
  voted: false
clipboard-check:
  aliases:
    unicodes:
      secondary:
        - 10f46c
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Clipboard with Check
  search:
    terms: []
  styles:
    - solid
  unicode: f46c
  voted: false
clipboard-list:
  aliases:
    unicodes:
      secondary:
        - 10f46d
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Clipboard List
  search:
    terms: []
  styles:
    - solid
  unicode: f46d
  voted: false
clipboard-question:
  changes:
    - 6.1.0
    - 6.2.0
  label: Clipboard Question
  search:
    terms: []
  styles:
    - solid
  unicode: e4e3
  voted: false
clipboard-user:
  aliases:
    unicodes:
      secondary:
        - 10f7f3
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Clipboard with User
  search:
    terms: []
  styles:
    - solid
  unicode: f7f3
  voted: false
clock:
  aliases:
    names:
      - clock-four
    unicodes:
      composite:
        - 1f553
      secondary:
        - 10f017
  changes:
    - 1.0.0
    - 5.0.0
    - 5.12.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Clock
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f017
  voted: false
clock-rotate-left:
  aliases:
    names:
      - history
    unicodes:
      secondary:
        - 10f1da
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Clock Rotate Left
  search:
    terms: []
  styles:
    - solid
  unicode: f1da
  voted: false
clone:
  aliases:
    unicodes:
      secondary:
        - 10f24d
  changes:
    - 4.4.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Clone
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f24d
  voted: false
closed-captioning:
  aliases:
    unicodes:
      secondary:
        - 10f20a
  changes:
    - 4.2.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Closed Captioning
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f20a
  voted: false
cloud:
  aliases:
    unicodes:
      composite:
        - '2601'
      secondary:
        - 10f0c2
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.11
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Cloud
  search:
    terms: []
  styles:
    - solid
  unicode: f0c2
  voted: false
cloud-arrow-down:
  aliases:
    names:
      - cloud-download
      - cloud-download-alt
    unicodes:
      composite:
        - f381
      primary:
        - f381
      secondary:
        - 10f0ed
        - 10f381
  changes:
    - 3.0.0
    - 5.0.0
    - 5.0.11
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Cloud arrow down
  search:
    terms: []
  styles:
    - solid
  unicode: f0ed
  voted: false
cloud-arrow-up:
  aliases:
    names:
      - cloud-upload
      - cloud-upload-alt
    unicodes:
      composite:
        - f382
      primary:
        - f382
      secondary:
        - 10f0ee
        - 10f382
  changes:
    - 3.0.0
    - 5.0.0
    - 5.0.11
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Cloud arrow up
  search:
    terms: []
  styles:
    - solid
  unicode: f0ee
  voted: false
cloud-bolt:
  aliases:
    names:
      - thunderstorm
    unicodes:
      composite:
        - 1f329
      secondary:
        - 10f76c
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Cloud bolt
  search:
    terms: []
  styles:
    - solid
  unicode: f76c
  voted: false
cloud-meatball:
  aliases:
    unicodes:
      secondary:
        - 10f73b
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cloud with (a chance of) Meatball
  search:
    terms: []
  styles:
    - solid
  unicode: f73b
  voted: false
cloud-moon:
  aliases:
    unicodes:
      secondary:
        - 10f6c3
  changes:
    - 5.4.0
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cloud with Moon
  search:
    terms: []
  styles:
    - solid
  unicode: f6c3
  voted: false
cloud-moon-rain:
  aliases:
    unicodes:
      secondary:
        - 10f73c
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cloud with Moon and Rain
  search:
    terms: []
  styles:
    - solid
  unicode: f73c
  voted: false
cloud-rain:
  aliases:
    unicodes:
      composite:
        - 1f327
        - 26c6
      secondary:
        - 10f73d
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cloud with Rain
  search:
    terms: []
  styles:
    - solid
  unicode: f73d
  voted: false
cloud-showers-heavy:
  aliases:
    unicodes:
      secondary:
        - 10f740
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cloud with Heavy Showers
  search:
    terms: []
  styles:
    - solid
  unicode: f740
  voted: false
cloud-showers-water:
  changes:
    - 6.1.0
    - 6.2.0
  label: Cloud Showers-water
  search:
    terms: []
  styles:
    - solid
  unicode: e4e4
  voted: false
cloud-sun:
  aliases:
    unicodes:
      composite:
        - 26c5
      secondary:
        - 10f6c4
  changes:
    - 5.4.0
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cloud with Sun
  search:
    terms: []
  styles:
    - solid
  unicode: f6c4
  voted: false
cloud-sun-rain:
  aliases:
    unicodes:
      composite:
        - 1f326
      secondary:
        - 10f743
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cloud with Sun and Rain
  search:
    terms: []
  styles:
    - solid
  unicode: f743
  voted: false
cloudflare:
  changes:
    - 5.15.0
  label: Cloudflare
  search:
    terms: []
  styles:
    - brands
  unicode: e07d
  voted: false
cloudscale:
  changes:
    - 5.0.0
  label: cloudscale.ch
  search:
    terms: []
  styles:
    - brands
  unicode: f383
  voted: false
cloudsmith:
  changes:
    - 5.0.0
  label: Cloudsmith
  search:
    terms: []
  styles:
    - brands
  unicode: f384
  voted: false
cloudversify:
  changes:
    - 5.0.0
  label: cloudversify
  search:
    terms: []
  styles:
    - brands
  unicode: f385
  voted: false
clover:
  changes:
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Clover
  search:
    terms: []
  styles:
    - solid
  unicode: e139
  voted: false
cmplid:
  changes:
    - 6.0.0-beta1
  label: Cmplid
  search:
    terms: []
  styles:
    - brands
  unicode: e360
  voted: false
code:
  aliases:
    unicodes:
      secondary:
        - 10f121
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Code
  search:
    terms: []
  styles:
    - solid
  unicode: f121
  voted: false
code-branch:
  aliases:
    unicodes:
      secondary:
        - 10f126
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Code Branch
  search:
    terms: []
  styles:
    - solid
  unicode: f126
  voted: false
code-commit:
  aliases:
    unicodes:
      secondary:
        - 10f386
  changes:
    - 5.0.0
    - 5.1.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Code Commit
  search:
    terms: []
  styles:
    - solid
  unicode: f386
  voted: false
code-compare:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0
    - 6.2.0
  label: Code Compare
  search:
    terms: []
  styles:
    - solid
  unicode: e13a
  voted: false
code-fork:
  changes:
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Code Fork
  search:
    terms: []
  styles:
    - solid
  unicode: e13b
  voted: false
code-merge:
  aliases:
    unicodes:
      secondary:
        - 10f387
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Code Merge
  search:
    terms: []
  styles:
    - solid
  unicode: f387
  voted: false
code-pull-request:
  changes:
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Code Pull Request
  search:
    terms: []
  styles:
    - solid
  unicode: e13c
  voted: false
codepen:
  changes:
    - 4.1.0
    - 5.0.0
  label: Codepen
  search:
    terms: []
  styles:
    - brands
  unicode: f1cb
  voted: false
codiepie:
  changes:
    - 4.5.0
    - 5.0.0
  label: Codie Pie
  search:
    terms: []
  styles:
    - brands
  unicode: f284
  voted: false
coins:
  aliases:
    unicodes:
      secondary:
        - 10f51e
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Coins
  search:
    terms: []
  styles:
    - solid
  unicode: f51e
  voted: true
colon-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Colon Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e140
  voted: false
comment:
  aliases:
    unicodes:
      composite:
        - 1f5e9
        - f0e5
      secondary:
        - 10f075
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.9
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: comment
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f075
  voted: false
comment-dollar:
  aliases:
    unicodes:
      secondary:
        - 10f651
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Comment Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f651
  voted: false
comment-dots:
  aliases:
    names:
      - commenting
    unicodes:
      composite:
        - 1f4ac
        - f27b
      secondary:
        - 10f4ad
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Comment Dots
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f4ad
  voted: false
comment-medical:
  aliases:
    unicodes:
      secondary:
        - 10f7f5
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Alternate Medical Chat
  search:
    terms: []
  styles:
    - solid
  unicode: f7f5
  voted: false
comment-slash:
  aliases:
    unicodes:
      secondary:
        - 10f4b3
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Comment Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4b3
  voted: false
comment-sms:
  aliases:
    names:
      - sms
    unicodes:
      secondary:
        - 10f7cd
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Comment sms
  search:
    terms: []
  styles:
    - solid
  unicode: f7cd
  voted: true
comments:
  aliases:
    unicodes:
      composite:
        - 1f5ea
        - f0e6
      secondary:
        - 10f086
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: comments
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f086
  voted: false
comments-dollar:
  aliases:
    unicodes:
      secondary:
        - 10f653
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Comments Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f653
  voted: false
compact-disc:
  aliases:
    unicodes:
      composite:
        - 1f4bf
        - 1f4c0
        - 1f5b8
      secondary:
        - 10f51f
  changes:
    - 5.0.13
    - 5.10.1
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Compact Disc
  search:
    terms: []
  styles:
    - solid
  unicode: f51f
  voted: true
compass:
  aliases:
    unicodes:
      composite:
        - 1f9ed
      secondary:
        - 10f14e
  changes:
    - 3.2.0
    - 5.0.0
    - 5.2.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Compass
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f14e
  voted: false
compass-drafting:
  aliases:
    names:
      - drafting-compass
    unicodes:
      secondary:
        - 10f568
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Compass drafting
  search:
    terms: []
  styles:
    - solid
  unicode: f568
  voted: false
compress:
  aliases:
    unicodes:
      secondary:
        - 10f066
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Compress
  search:
    terms: []
  styles:
    - solid
  unicode: f066
  voted: false
computer:
  changes:
    - 6.1.0
    - 6.2.0
  label: Computer
  search:
    terms: []
  styles:
    - solid
  unicode: e4e5
  voted: false
computer-mouse:
  aliases:
    names:
      - mouse
    unicodes:
      composite:
        - 1f5b1
      secondary:
        - 10f8cc
  changes:
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Computer mouse
  search:
    terms: []
  styles:
    - solid
  unicode: f8cc
  voted: true
confluence:
  changes:
    - 5.6.0
  label: Confluence
  search:
    terms: []
  styles:
    - brands
  unicode: f78d
  voted: true
connectdevelop:
  changes:
    - 4.3.0
    - 5.0.0
  label: Connect Develop
  search:
    terms: []
  styles:
    - brands
  unicode: f20e
  voted: false
contao:
  changes:
    - 4.4.0
    - 5.0.0
  label: Contao
  search:
    terms: []
  styles:
    - brands
  unicode: f26d
  voted: false
cookie:
  aliases:
    unicodes:
      composite:
        - 1f36a
      secondary:
        - 10f563
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cookie
  search:
    terms: []
  styles:
    - solid
  unicode: f563
  voted: true
cookie-bite:
  aliases:
    unicodes:
      secondary:
        - 10f564
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cookie Bite
  search:
    terms: []
  styles:
    - solid
  unicode: f564
  voted: true
copy:
  aliases:
    unicodes:
      secondary:
        - 10f0c5
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Copy
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0c5
  voted: false
copyright:
  aliases:
    unicodes:
      composite:
        - a9
      secondary:
        - 10f1f9
  changes:
    - 4.2.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Copyright
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1f9
  voted: false
cotton-bureau:
  changes:
    - 5.10.0
  label: Cotton Bureau
  search:
    terms: []
  styles:
    - brands
  unicode: f89e
  voted: false
couch:
  aliases:
    unicodes:
      secondary:
        - 10f4b8
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Couch
  search:
    terms: []
  styles:
    - solid
  unicode: f4b8
  voted: false
cow:
  aliases:
    unicodes:
      composite:
        - 1f404
      secondary:
        - 10f6c8
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Cow
  search:
    terms: []
  styles:
    - solid
  unicode: f6c8
  voted: false
cpanel:
  changes:
    - 5.0.0
  label: cPanel
  search:
    terms: []
  styles:
    - brands
  unicode: f388
  voted: false
creative-commons:
  changes:
    - 4.4.0
    - 5.0.0
    - 5.0.11
    - 5.1.0
  label: Creative Commons
  search:
    terms: []
  styles:
    - brands
  unicode: f25e
  voted: false
creative-commons-by:
  changes:
    - 5.0.11
  label: Creative Commons Attribution
  search:
    terms: []
  styles:
    - brands
  unicode: f4e7
  voted: false
creative-commons-nc:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial
  search:
    terms: []
  styles:
    - brands
  unicode: f4e8
  voted: false
creative-commons-nc-eu:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial (Euro Sign)
  search:
    terms: []
  styles:
    - brands
  unicode: f4e9
  voted: false
creative-commons-nc-jp:
  changes:
    - 5.0.11
  label: Creative Commons Noncommercial (Yen Sign)
  search:
    terms: []
  styles:
    - brands
  unicode: f4ea
  voted: false
creative-commons-nd:
  changes:
    - 5.0.11
  label: Creative Commons No Derivative Works
  search:
    terms: []
  styles:
    - brands
  unicode: f4eb
  voted: false
creative-commons-pd:
  changes:
    - 5.0.11
  label: Creative Commons Public Domain
  search:
    terms: []
  styles:
    - brands
  unicode: f4ec
  voted: false
creative-commons-pd-alt:
  changes:
    - 5.0.11
  label: Alternate Creative Commons Public Domain
  search:
    terms: []
  styles:
    - brands
  unicode: f4ed
  voted: false
creative-commons-remix:
  changes:
    - 5.0.11
  label: Creative Commons Remix
  search:
    terms: []
  styles:
    - brands
  unicode: f4ee
  voted: false
creative-commons-sa:
  changes:
    - 5.0.11
  label: Creative Commons Share Alike
  search:
    terms: []
  styles:
    - brands
  unicode: f4ef
  voted: false
creative-commons-sampling:
  changes:
    - 5.0.11
  label: Creative Commons Sampling
  search:
    terms: []
  styles:
    - brands
  unicode: f4f0
  voted: false
creative-commons-sampling-plus:
  changes:
    - 5.0.11
  label: Creative Commons Sampling +
  search:
    terms: []
  styles:
    - brands
  unicode: f4f1
  voted: false
creative-commons-share:
  changes:
    - 5.0.11
  label: Creative Commons Share
  search:
    terms: []
  styles:
    - brands
  unicode: f4f2
  voted: false
creative-commons-zero:
  changes:
    - 5.0.11
    - 5.4.0
  label: Creative Commons CC0
  search:
    terms: []
  styles:
    - brands
  unicode: f4f3
  voted: false
credit-card:
  aliases:
    names:
      - credit-card-alt
    unicodes:
      composite:
        - 1f4b3
        - f283
      secondary:
        - 10f09d
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Credit Card
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f09d
  voted: false
critical-role:
  changes:
    - 5.4.0
    - 5.8.0
  label: Critical Role
  search:
    terms: []
  styles:
    - brands
  unicode: f6c9
  voted: false
crop:
  aliases:
    unicodes:
      secondary:
        - 10f125
  changes:
    - 3.1.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: crop
  search:
    terms: []
  styles:
    - solid
  unicode: f125
  voted: false
crop-simple:
  aliases:
    names:
      - crop-alt
    unicodes:
      secondary:
        - 10f565
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Crop simple
  search:
    terms: []
  styles:
    - solid
  unicode: f565
  voted: false
cross:
  aliases:
    unicodes:
      composite:
        - 1f547
        - 271d
      secondary:
        - 10f654
  changes:
    - 5.3.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Cross
  search:
    terms: []
  styles:
    - solid
  unicode: f654
  voted: false
crosshairs:
  aliases:
    unicodes:
      secondary:
        - 10f05b
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Crosshairs
  search:
    terms: []
  styles:
    - solid
  unicode: f05b
  voted: false
crow:
  aliases:
    unicodes:
      secondary:
        - 10f520
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Crow
  search:
    terms: []
  styles:
    - solid
  unicode: f520
  voted: false
crown:
  aliases:
    unicodes:
      composite:
        - 1f451
      secondary:
        - 10f521
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Crown
  search:
    terms: []
  styles:
    - solid
  unicode: f521
  voted: true
crutch:
  aliases:
    unicodes:
      secondary:
        - 10f7f7
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Crutch
  search:
    terms: []
  styles:
    - solid
  unicode: f7f7
  voted: false
cruzeiro-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cruzeiro Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e152
  voted: false
css3:
  changes:
    - 3.1.0
    - 5.0.0
  label: CSS 3 Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f13c
  voted: false
css3-alt:
  changes:
    - 5.0.0
  label: Alternate CSS3 Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f38b
  voted: false
cube:
  aliases:
    unicodes:
      secondary:
        - 10f1b2
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cube
  search:
    terms: []
  styles:
    - solid
  unicode: f1b2
  voted: false
cubes:
  aliases:
    unicodes:
      secondary:
        - 10f1b3
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Cubes
  search:
    terms: []
  styles:
    - solid
  unicode: f1b3
  voted: false
cubes-stacked:
  changes:
    - 6.1.0
    - 6.1.1
    - 6.2.0
  label: Cubes Stacked
  search:
    terms: []
  styles:
    - solid
  unicode: e4e6
  voted: false
cuttlefish:
  changes:
    - 5.0.0
  label: Cuttlefish
  search:
    terms: []
  styles:
    - brands
  unicode: f38c
  voted: false
d:
  aliases:
    unicodes:
      composite:
        - '64'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: D
  search:
    terms: []
  styles:
    - solid
  unicode: '44'
  voted: false
d-and-d:
  changes:
    - 5.0.0
  label: Dungeons & Dragons
  search:
    terms: []
  styles:
    - brands
  unicode: f38d
  voted: false
d-and-d-beyond:
  changes:
    - 5.4.0
  label: D&D Beyond
  search:
    terms: []
  styles:
    - brands
  unicode: f6ca
  voted: false
dailymotion:
  changes:
    - 5.12.1
    - 5.14.0
  label: dailymotion
  search:
    terms: []
  styles:
    - brands
  unicode: e052
  voted: true
dashcube:
  changes:
    - 4.3.0
    - 5.0.0
    - 5.0.3
  label: DashCube
  search:
    terms: []
  styles:
    - brands
  unicode: f210
  voted: false
database:
  aliases:
    unicodes:
      secondary:
        - 10f1c0
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Database
  search:
    terms: []
  styles:
    - solid
  unicode: f1c0
  voted: false
deezer:
  changes:
    - 5.13.1
    - 5.14.0
  label: Deezer
  search:
    terms: []
  styles:
    - brands
  unicode: e077
  voted: true
delete-left:
  aliases:
    names:
      - backspace
    unicodes:
      composite:
        - 232b
      secondary:
        - 10f55a
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Delete left
  search:
    terms: []
  styles:
    - solid
  unicode: f55a
  voted: true
delicious:
  changes:
    - 4.1.0
    - 5.0.0
    - 5.7.0
  label: Delicious
  search:
    terms: []
  styles:
    - brands
  unicode: f1a5
  voted: false
democrat:
  aliases:
    unicodes:
      secondary:
        - 10f747
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Democrat
  search:
    terms: []
  styles:
    - solid
  unicode: f747
  voted: false
deploydog:
  changes:
    - 5.0.0
  label: deploy.dog
  search:
    terms: []
  styles:
    - brands
  unicode: f38e
  voted: false
deskpro:
  changes:
    - 5.0.0
  label: Deskpro
  search:
    terms: []
  styles:
    - brands
  unicode: f38f
  voted: false
desktop:
  aliases:
    names:
      - desktop-alt
    unicodes:
      composite:
        - 1f5a5
        - f108
      primary:
        - f108
      secondary:
        - 10f108
        - 10f390
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Desktop
  search:
    terms: []
  styles:
    - solid
  unicode: f390
  voted: false
dev:
  changes:
    - 5.4.0
  label: DEV
  search:
    terms: []
  styles:
    - brands
  unicode: f6cc
  voted: true
deviantart:
  changes:
    - 4.1.0
    - 5.0.0
  label: deviantART
  search:
    terms: []
  styles:
    - brands
  unicode: f1bd
  voted: false
dharmachakra:
  aliases:
    unicodes:
      composite:
        - '2638'
      secondary:
        - 10f655
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Dharmachakra
  search:
    terms: []
  styles:
    - solid
  unicode: f655
  voted: false
dhl:
  changes:
    - 5.6.0
  label: DHL
  search:
    terms: []
  styles:
    - brands
  unicode: f790
  voted: false
diagram-next:
  changes:
    - 6.0.0-beta3
    - 6.2.0
  label: Diagram Next
  search:
    terms: []
  styles:
    - solid
  unicode: e476
  voted: false
diagram-predecessor:
  changes:
    - 6.0.0-beta3
    - 6.2.0
  label: Diagram Predecessor
  search:
    terms: []
  styles:
    - solid
  unicode: e477
  voted: false
diagram-project:
  aliases:
    names:
      - project-diagram
    unicodes:
      secondary:
        - 10f542
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Project Diagram
  search:
    terms: []
  styles:
    - solid
  unicode: f542
  voted: false
diagram-successor:
  changes:
    - 6.0.0-beta3
    - 6.2.0
  label: Diagram Successor
  search:
    terms: []
  styles:
    - solid
  unicode: e47a
  voted: false
diamond:
  aliases:
    unicodes:
      composite:
        - '2666'
      secondary:
        - 10f219
  changes:
    - 4.3.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Diamond
  search:
    terms: []
  styles:
    - solid
  unicode: f219
  voted: false
diamond-turn-right:
  aliases:
    names:
      - directions
    unicodes:
      secondary:
        - 10f5eb
  changes:
    - 5.2.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Diamond turn right
  search:
    terms: []
  styles:
    - solid
  unicode: f5eb
  voted: false
diaspora:
  changes:
    - 5.6.0
    - 5.8.0
  label: Diaspora
  search:
    terms: []
  styles:
    - brands
  unicode: f791
  voted: true
dice:
  aliases:
    unicodes:
      composite:
        - 1f3b2
      secondary:
        - 10f522
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Dice
  search:
    terms: []
  styles:
    - solid
  unicode: f522
  voted: true
dice-d20:
  aliases:
    unicodes:
      secondary:
        - 10f6cf
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Dice D20
  search:
    terms: []
  styles:
    - solid
  unicode: f6cf
  voted: true
dice-d6:
  aliases:
    unicodes:
      secondary:
        - 10f6d1
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Dice D6
  search:
    terms: []
  styles:
    - solid
  unicode: f6d1
  voted: false
dice-five:
  aliases:
    unicodes:
      composite:
        - '2684'
      secondary:
        - 10f523
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Dice Five
  search:
    terms: []
  styles:
    - solid
  unicode: f523
  voted: true
dice-four:
  aliases:
    unicodes:
      composite:
        - '2683'
      secondary:
        - 10f524
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Dice Four
  search:
    terms: []
  styles:
    - solid
  unicode: f524
  voted: true
dice-one:
  aliases:
    unicodes:
      composite:
        - '2680'
      secondary:
        - 10f525
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Dice One
  search:
    terms: []
  styles:
    - solid
  unicode: f525
  voted: true
dice-six:
  aliases:
    unicodes:
      composite:
        - '2685'
      secondary:
        - 10f526
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Dice Six
  search:
    terms: []
  styles:
    - solid
  unicode: f526
  voted: true
dice-three:
  aliases:
    unicodes:
      composite:
        - '2682'
      secondary:
        - 10f527
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Dice Three
  search:
    terms: []
  styles:
    - solid
  unicode: f527
  voted: true
dice-two:
  aliases:
    unicodes:
      composite:
        - '2681'
      secondary:
        - 10f528
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Dice Two
  search:
    terms: []
  styles:
    - solid
  unicode: f528
  voted: true
digg:
  changes:
    - 4.1.0
    - 5.0.0
  label: Digg Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a6
  voted: false
digital-ocean:
  changes:
    - 5.0.0
    - 5.7.0
  label: Digital Ocean
  search:
    terms: []
  styles:
    - brands
  unicode: f391
  voted: false
discord:
  changes:
    - 5.0.0
    - 5.15.4
    - 6.0.0-beta1
  label: Discord
  search:
    terms: []
  styles:
    - brands
  unicode: f392
  voted: false
discourse:
  changes:
    - 5.0.0
    - 5.0.3
  label: Discourse
  search:
    terms: []
  styles:
    - brands
  unicode: f393
  voted: false
disease:
  aliases:
    unicodes:
      secondary:
        - 10f7fa
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Disease
  search:
    terms: []
  styles:
    - solid
  unicode: f7fa
  voted: false
display:
  changes:
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Display
  search:
    terms: []
  styles:
    - solid
  unicode: e163
  voted: false
divide:
  aliases:
    unicodes:
      composite:
        - '2797'
        - f7
      secondary:
        - 10f529
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Divide
  search:
    terms: []
  styles:
    - solid
  unicode: f529
  voted: false
dna:
  aliases:
    unicodes:
      composite:
        - 1f9ec
      secondary:
        - 10f471
  changes:
    - 5.0.7
    - 5.0.10
    - 6.0.0-beta1
    - 6.2.0
  label: DNA
  search:
    terms: []
  styles:
    - solid
  unicode: f471
  voted: false
dochub:
  changes:
    - 5.0.0
  label: DocHub
  search:
    terms: []
  styles:
    - brands
  unicode: f394
  voted: false
docker:
  changes:
    - 5.0.0
  label: Docker
  search:
    terms: []
  styles:
    - brands
  unicode: f395
  voted: false
dog:
  aliases:
    unicodes:
      composite:
        - 1f415
      secondary:
        - 10f6d3
  changes:
    - 5.4.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Dog
  search:
    terms: []
  styles:
    - solid
  unicode: f6d3
  voted: false
dollar-sign:
  aliases:
    names:
      - dollar
      - usd
    unicodes:
      composite:
        - 1f4b2
        - f155
      primary:
        - f155
      secondary:
        - '1024'
        - 10f155
  changes:
    - 3.2.0
    - 5.0.0
    - 5.0.9
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Dollar Sign
  search:
    terms: []
  styles:
    - solid
  unicode: '24'
  voted: false
dolly:
  aliases:
    names:
      - dolly-box
    unicodes:
      secondary:
        - 10f472
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Dolly
  search:
    terms: []
  styles:
    - solid
  unicode: f472
  voted: false
dong-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Dong Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e169
  voted: false
door-closed:
  aliases:
    unicodes:
      composite:
        - 1f6aa
      secondary:
        - 10f52a
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Door Closed
  search:
    terms: []
  styles:
    - solid
  unicode: f52a
  voted: true
door-open:
  aliases:
    unicodes:
      secondary:
        - 10f52b
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Door Open
  search:
    terms: []
  styles:
    - solid
  unicode: f52b
  voted: true
dove:
  aliases:
    unicodes:
      composite:
        - 1f54a
      secondary:
        - 10f4ba
  changes:
    - 5.0.9
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Dove
  search:
    terms: []
  styles:
    - solid
  unicode: f4ba
  voted: false
down-left-and-up-right-to-center:
  aliases:
    names:
      - compress-alt
    unicodes:
      secondary:
        - 10f422
  changes:
    - 1.0.0
    - 5.0.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Down left and up right to center
  search:
    terms: []
  styles:
    - solid
  unicode: f422
  voted: false
down-long:
  aliases:
    names:
      - long-arrow-alt-down
    unicodes:
      secondary:
        - 10f309
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Down long
  search:
    terms: []
  styles:
    - solid
  unicode: f309
  voted: false
download:
  aliases:
    unicodes:
      secondary:
        - 10f019
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Download
  search:
    terms: []
  styles:
    - solid
  unicode: f019
  voted: false
draft2digital:
  changes:
    - 5.0.0
  label: Draft2digital
  search:
    terms: []
  styles:
    - brands
  unicode: f396
  voted: false
dragon:
  aliases:
    unicodes:
      composite:
        - 1f409
      secondary:
        - 10f6d5
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Dragon
  search:
    terms: []
  styles:
    - solid
  unicode: f6d5
  voted: false
draw-polygon:
  aliases:
    unicodes:
      secondary:
        - 10f5ee
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Draw Polygon
  search:
    terms: []
  styles:
    - solid
  unicode: f5ee
  voted: false
dribbble:
  changes:
    - 5.0.0
  label: Dribbble
  search:
    terms: []
  styles:
    - brands
  unicode: f17d
  voted: false
dropbox:
  changes:
    - 3.2.0
    - 5.0.0
    - 5.0.1
  label: Dropbox
  search:
    terms: []
  styles:
    - brands
  unicode: f16b
  voted: false
droplet:
  aliases:
    names:
      - tint
    unicodes:
      composite:
        - 1f4a7
      secondary:
        - 10f043
  changes:
    - 1.0.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Droplet
  search:
    terms: []
  styles:
    - solid
  unicode: f043
  voted: false
droplet-slash:
  aliases:
    names:
      - tint-slash
    unicodes:
      secondary:
        - 10f5c7
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Droplet slash
  search:
    terms: []
  styles:
    - solid
  unicode: f5c7
  voted: false
drum:
  aliases:
    unicodes:
      composite:
        - 1f941
      secondary:
        - 10f569
  changes:
    - 5.1.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Drum
  search:
    terms: []
  styles:
    - solid
  unicode: f569
  voted: true
drum-steelpan:
  aliases:
    unicodes:
      secondary:
        - 10f56a
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Drum Steelpan
  search:
    terms: []
  styles:
    - solid
  unicode: f56a
  voted: false
drumstick-bite:
  aliases:
    unicodes:
      secondary:
        - 10f6d7
  changes:
    - 5.4.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Drumstick with Bite Taken Out
  search:
    terms: []
  styles:
    - solid
  unicode: f6d7
  voted: false
drupal:
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
  label: Drupal Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a9
  voted: false
dumbbell:
  aliases:
    unicodes:
      secondary:
        - 10f44b
  changes:
    - 5.0.5
    - 6.0.0-beta1
    - 6.2.0
  label: Dumbbell
  search:
    terms: []
  styles:
    - solid
  unicode: f44b
  voted: false
dumpster:
  aliases:
    unicodes:
      secondary:
        - 10f793
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Dumpster
  search:
    terms: []
  styles:
    - solid
  unicode: f793
  voted: true
dumpster-fire:
  aliases:
    unicodes:
      secondary:
        - 10f794
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Dumpster Fire
  search:
    terms: []
  styles:
    - solid
  unicode: f794
  voted: true
dungeon:
  aliases:
    unicodes:
      secondary:
        - 10f6d9
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Dungeon
  search:
    terms: []
  styles:
    - solid
  unicode: f6d9
  voted: false
dyalog:
  changes:
    - 5.0.0
  label: Dyalog
  search:
    terms: []
  styles:
    - brands
  unicode: f399
  voted: false
e:
  aliases:
    unicodes:
      composite:
        - '65'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: E
  search:
    terms: []
  styles:
    - solid
  unicode: '45'
  voted: false
ear-deaf:
  aliases:
    names:
      - deaf
      - deafness
      - hard-of-hearing
    unicodes:
      secondary:
        - 10f2a4
  changes:
    - 4.6.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Ear deaf
  search:
    terms: []
  styles:
    - solid
  unicode: f2a4
  voted: false
ear-listen:
  aliases:
    names:
      - assistive-listening-systems
    unicodes:
      secondary:
        - 10f2a2
  changes:
    - 4.6.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Ear listen
  search:
    terms: []
  styles:
    - solid
  unicode: f2a2
  voted: false
earlybirds:
  changes:
    - 5.0.0
  label: Earlybirds
  search:
    terms: []
  styles:
    - brands
  unicode: f39a
  voted: false
earth-africa:
  aliases:
    names:
      - globe-africa
    unicodes:
      composite:
        - 1f30d
      secondary:
        - 10f57c
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Earth Africa
  search:
    terms: []
  styles:
    - solid
  unicode: f57c
  voted: false
earth-americas:
  aliases:
    names:
      - earth
      - earth-america
      - globe-americas
    unicodes:
      composite:
        - 1f30e
      secondary:
        - 10f57d
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Earth americas
  search:
    terms: []
  styles:
    - solid
  unicode: f57d
  voted: false
earth-asia:
  aliases:
    names:
      - globe-asia
    unicodes:
      composite:
        - 1f30f
      secondary:
        - 10f57e
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Earth Asia
  search:
    terms: []
  styles:
    - solid
  unicode: f57e
  voted: false
earth-europe:
  aliases:
    names:
      - globe-europe
    unicodes:
      secondary:
        - 10f7a2
  changes:
    - 5.6.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Earth Europe
  search:
    terms: []
  styles:
    - solid
  unicode: f7a2
  voted: true
earth-oceania:
  aliases:
    names:
      - globe-oceania
  changes:
    - 6.0.0-beta3
    - 6.2.0
  label: Earth Oceania
  search:
    terms: []
  styles:
    - solid
  unicode: e47b
  voted: false
ebay:
  changes:
    - 5.0.11
    - 5.7.0
  label: eBay
  search:
    terms: []
  styles:
    - brands
  unicode: f4f4
  voted: true
edge:
  changes:
    - 4.5.0
    - 5.0.0
    - 5.12.1
    - 6.1.2
  label: Edge Browser
  search:
    terms: []
  styles:
    - brands
  unicode: f282
  voted: false
edge-legacy:
  changes:
    - 5.13.1
    - 5.14.0
  label: Edge Legacy Browser
  search:
    terms: []
  styles:
    - brands
  unicode: e078
  voted: false
egg:
  aliases:
    unicodes:
      composite:
        - 1f95a
      secondary:
        - 10f7fb
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Egg
  search:
    terms: []
  styles:
    - solid
  unicode: f7fb
  voted: false
eject:
  aliases:
    unicodes:
      composite:
        - 23cf
      secondary:
        - 10f052
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: eject
  search:
    terms: []
  styles:
    - solid
  unicode: f052
  voted: false
elementor:
  changes:
    - 5.0.3
    - 6.1.0
  label: Elementor
  search:
    terms: []
  styles:
    - brands
  unicode: f430
  voted: true
elevator:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Elevator
  search:
    terms: []
  styles:
    - solid
  unicode: e16d
  voted: true
ellipsis:
  aliases:
    names:
      - ellipsis-h
    unicodes:
      secondary:
        - 10f141
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Ellipsis
  search:
    terms: []
  styles:
    - solid
  unicode: f141
  voted: false
ellipsis-vertical:
  aliases:
    names:
      - ellipsis-v
    unicodes:
      secondary:
        - 10f142
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Ellipsis vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f142
  voted: false
ello:
  changes:
    - 5.2.0
  label: Ello
  search:
    terms: []
  styles:
    - brands
  unicode: f5f1
  voted: true
ember:
  changes:
    - 5.0.0
    - 5.0.3
  label: Ember
  search:
    terms: []
  styles:
    - brands
  unicode: f423
  voted: false
empire:
  changes:
    - 4.1.0
    - 5.0.0
  label: Galactic Empire
  search:
    terms: []
  styles:
    - brands
  unicode: f1d1
  voted: false
envelope:
  aliases:
    unicodes:
      composite:
        - 1f582
        - '2709'
        - f003
      secondary:
        - 10f0e0
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Envelope
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0e0
  voted: false
envelope-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Envelope Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e4e8
  voted: false
envelope-open:
  aliases:
    unicodes:
      composite:
        - f2b7
      secondary:
        - 10f2b6
  changes:
    - 4.7.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Envelope Open
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2b6
  voted: false
envelope-open-text:
  aliases:
    unicodes:
      secondary:
        - 10f658
  changes:
    - 5.3.0
    - 5.10.1
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Envelope Open-text
  search:
    terms: []
  styles:
    - solid
  unicode: f658
  voted: false
envelopes-bulk:
  aliases:
    names:
      - mail-bulk
    unicodes:
      secondary:
        - 10f674
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Envelopes bulk
  search:
    terms: []
  styles:
    - solid
  unicode: f674
  voted: false
envira:
  changes:
    - 4.6.0
    - 5.0.0
  label: Envira Gallery
  search:
    terms: []
  styles:
    - brands
  unicode: f299
  voted: false
equals:
  aliases:
    unicodes:
      composite:
        - f52c
      primary:
        - f52c
      secondary:
        - 103d
        - 10f52c
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Equals
  search:
    terms: []
  styles:
    - solid
  unicode: 3d
  voted: false
eraser:
  aliases:
    unicodes:
      secondary:
        - 10f12d
  changes:
    - 3.1.0
    - 5.0.0
    - 5.8.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: eraser
  search:
    terms: []
  styles:
    - solid
  unicode: f12d
  voted: false
erlang:
  changes:
    - 5.0.0
    - 5.0.3
    - 5.7.0
  label: Erlang
  search:
    terms: []
  styles:
    - brands
  unicode: f39d
  voted: false
ethereum:
  changes:
    - 5.0.2
  label: Ethereum
  search:
    terms: []
  styles:
    - brands
  unicode: f42e
  voted: true
ethernet:
  aliases:
    unicodes:
      secondary:
        - 10f796
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Ethernet
  search:
    terms: []
  styles:
    - solid
  unicode: f796
  voted: true
etsy:
  changes:
    - 4.7.0
    - 5.0.0
  label: Etsy
  search:
    terms: []
  styles:
    - brands
  unicode: f2d7
  voted: false
euro-sign:
  aliases:
    names:
      - eur
      - euro
    unicodes:
      composite:
        - 20ac
      secondary:
        - 10f153
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Euro Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f153
  voted: false
evernote:
  changes:
    - 5.8.0
  label: Evernote
  search:
    terms: []
  styles:
    - brands
  unicode: f839
  voted: false
exclamation:
  aliases:
    unicodes:
      composite:
        - '2755'
        - '2757'
        - f12a
      primary:
        - f12a
      secondary:
        - '1021'
        - 10f12a
  changes:
    - 3.1.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: '21'
  voted: false
expand:
  aliases:
    unicodes:
      secondary:
        - 10f065
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Expand
  search:
    terms: []
  styles:
    - solid
  unicode: f065
  voted: false
expeditedssl:
  changes:
    - 4.4.0
    - 5.0.0
  label: ExpeditedSSL
  search:
    terms: []
  styles:
    - brands
  unicode: f23e
  voted: false
explosion:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Explosion
  search:
    terms: []
  styles:
    - solid
  unicode: e4e9
  voted: false
eye:
  aliases:
    unicodes:
      composite:
        - 1f441
      secondary:
        - 10f06e
  changes:
    - 1.0.0
    - 5.0.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Eye
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f06e
  voted: false
eye-dropper:
  aliases:
    names:
      - eye-dropper-empty
      - eyedropper
    unicodes:
      secondary:
        - 10f1fb
  changes:
    - 4.2.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Eye Dropper
  search:
    terms: []
  styles:
    - solid
  unicode: f1fb
  voted: false
eye-low-vision:
  aliases:
    names:
      - low-vision
    unicodes:
      secondary:
        - 10f2a8
  changes:
    - 4.6.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Eye low vision
  search:
    terms: []
  styles:
    - solid
  unicode: f2a8
  voted: false
eye-slash:
  aliases:
    unicodes:
      secondary:
        - 10f070
  changes:
    - 1.0.0
    - 5.0.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Eye Slash
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f070
  voted: false
f:
  aliases:
    unicodes:
      composite:
        - '66'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: F
  search:
    terms: []
  styles:
    - solid
  unicode: '46'
  voted: false
face-angry:
  aliases:
    names:
      - angry
    unicodes:
      composite:
        - 1f620
      secondary:
        - 10f556
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face angry
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f556
  voted: false
face-dizzy:
  aliases:
    names:
      - dizzy
    unicodes:
      secondary:
        - 10f567
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face dizzy
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f567
  voted: false
face-flushed:
  aliases:
    names:
      - flushed
    unicodes:
      composite:
        - 1f633
      secondary:
        - 10f579
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face flushed
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f579
  voted: false
face-frown:
  aliases:
    names:
      - frown
    unicodes:
      composite:
        - '2639'
      secondary:
        - 10f119
  changes:
    - 3.1.0
    - 5.0.0
    - 5.0.9
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face frown
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f119
  voted: false
face-frown-open:
  aliases:
    names:
      - frown-open
    unicodes:
      composite:
        - 1f626
      secondary:
        - 10f57a
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face frown open
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f57a
  voted: false
face-grimace:
  aliases:
    names:
      - grimace
    unicodes:
      composite:
        - 1f62c
      secondary:
        - 10f57f
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grimace
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f57f
  voted: false
face-grin:
  aliases:
    names:
      - grin
    unicodes:
      composite:
        - 1f600
      secondary:
        - 10f580
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f580
  voted: false
face-grin-beam:
  aliases:
    names:
      - grin-beam
    unicodes:
      composite:
        - 1f604
      secondary:
        - 10f582
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin beam
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f582
  voted: false
face-grin-beam-sweat:
  aliases:
    names:
      - grin-beam-sweat
    unicodes:
      composite:
        - 1f605
      secondary:
        - 10f583
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin beam sweat
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f583
  voted: false
face-grin-hearts:
  aliases:
    names:
      - grin-hearts
    unicodes:
      composite:
        - 1f60d
      secondary:
        - 10f584
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin hearts
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f584
  voted: false
face-grin-squint:
  aliases:
    names:
      - grin-squint
    unicodes:
      composite:
        - 1f606
      secondary:
        - 10f585
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin squint
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f585
  voted: false
face-grin-squint-tears:
  aliases:
    names:
      - grin-squint-tears
    unicodes:
      composite:
        - 1f923
      secondary:
        - 10f586
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin squint tears
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f586
  voted: false
face-grin-stars:
  aliases:
    names:
      - grin-stars
    unicodes:
      composite:
        - 1f929
      secondary:
        - 10f587
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin stars
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f587
  voted: false
face-grin-tears:
  aliases:
    names:
      - grin-tears
    unicodes:
      composite:
        - 1f602
      secondary:
        - 10f588
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin tears
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f588
  voted: false
face-grin-tongue:
  aliases:
    names:
      - grin-tongue
    unicodes:
      composite:
        - 1f61b
      secondary:
        - 10f589
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin tongue
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f589
  voted: false
face-grin-tongue-squint:
  aliases:
    names:
      - grin-tongue-squint
    unicodes:
      composite:
        - 1f61d
      secondary:
        - 10f58a
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin tongue squint
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f58a
  voted: false
face-grin-tongue-wink:
  aliases:
    names:
      - grin-tongue-wink
    unicodes:
      composite:
        - 1f61c
      secondary:
        - 10f58b
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 5.12.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin tongue wink
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f58b
  voted: false
face-grin-wide:
  aliases:
    names:
      - grin-alt
    unicodes:
      composite:
        - 1f603
      secondary:
        - 10f581
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin wide
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f581
  voted: false
face-grin-wink:
  aliases:
    names:
      - grin-wink
    unicodes:
      secondary:
        - 10f58c
  changes:
    - 5.1.0
    - 5.1.1
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face grin wink
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f58c
  voted: false
face-kiss:
  aliases:
    names:
      - kiss
    unicodes:
      composite:
        - 1f617
      secondary:
        - 10f596
  changes:
    - 5.1.0
    - 5.1.1
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face kiss
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f596
  voted: false
face-kiss-beam:
  aliases:
    names:
      - kiss-beam
    unicodes:
      composite:
        - 1f619
      secondary:
        - 10f597
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Kiss Beam
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f597
  voted: false
face-kiss-wink-heart:
  aliases:
    names:
      - kiss-wink-heart
    unicodes:
      composite:
        - 1f618
      secondary:
        - 10f598
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Kiss Wink Heart
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f598
  voted: false
face-laugh:
  aliases:
    names:
      - laugh
    unicodes:
      secondary:
        - 10f599
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Laugh
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f599
  voted: false
face-laugh-beam:
  aliases:
    names:
      - laugh-beam
    unicodes:
      composite:
        - 1f601
      secondary:
        - 10f59a
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Laugh Beam
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f59a
  voted: false
face-laugh-squint:
  aliases:
    names:
      - laugh-squint
    unicodes:
      secondary:
        - 10f59b
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Laugh Squint
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f59b
  voted: false
face-laugh-wink:
  aliases:
    names:
      - laugh-wink
    unicodes:
      secondary:
        - 10f59c
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Laugh Wink
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f59c
  voted: false
face-meh:
  aliases:
    names:
      - meh
    unicodes:
      composite:
        - 1f610
      secondary:
        - 10f11a
  changes:
    - 3.1.0
    - 5.0.0
    - 5.0.9
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face meh
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f11a
  voted: false
face-meh-blank:
  aliases:
    names:
      - meh-blank
    unicodes:
      composite:
        - 1f636
      secondary:
        - 10f5a4
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Meh Blank
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5a4
  voted: false
face-rolling-eyes:
  aliases:
    names:
      - meh-rolling-eyes
    unicodes:
      composite:
        - 1f644
      secondary:
        - 10f5a5
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Rolling Eyes
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5a5
  voted: false
face-sad-cry:
  aliases:
    names:
      - sad-cry
    unicodes:
      composite:
        - 1f62d
      secondary:
        - 10f5b3
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Sad Cry
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5b3
  voted: false
face-sad-tear:
  aliases:
    names:
      - sad-tear
    unicodes:
      composite:
        - 1f622
      secondary:
        - 10f5b4
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Sad Tear
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5b4
  voted: false
face-smile:
  aliases:
    names:
      - smile
    unicodes:
      composite:
        - 1f642
      secondary:
        - 10f118
  changes:
    - 3.1.0
    - 5.0.0
    - 5.0.9
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Smile
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f118
  voted: false
face-smile-beam:
  aliases:
    names:
      - smile-beam
    unicodes:
      composite:
        - 1f60a
      secondary:
        - 10f5b8
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Smile Beam
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5b8
  voted: false
face-smile-wink:
  aliases:
    names:
      - smile-wink
    unicodes:
      composite:
        - 1f609
      secondary:
        - 10f4da
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Smile Wink
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f4da
  voted: false
face-surprise:
  aliases:
    names:
      - surprise
    unicodes:
      composite:
        - 1f62e
      secondary:
        - 10f5c2
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Surprise
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5c2
  voted: false
face-tired:
  aliases:
    names:
      - tired
    unicodes:
      composite:
        - 1f62b
      secondary:
        - 10f5c8
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Face Tired
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5c8
  voted: false
facebook:
  aliases:
    unicodes:
      composite:
        - f230
  changes:
    - 2.0.0
    - 5.0.0
    - 5.8.2
  label: Facebook
  search:
    terms: []
  styles:
    - brands
  unicode: f09a
  voted: false
facebook-f:
  changes:
    - 5.0.0
    - 5.8.2
  label: Facebook F
  search:
    terms: []
  styles:
    - brands
  unicode: f39e
  voted: false
facebook-messenger:
  changes:
    - 5.0.0
    - 5.8.2
    - 5.9.0
  label: Facebook Messenger
  search:
    terms: []
  styles:
    - brands
  unicode: f39f
  voted: false
fan:
  aliases:
    unicodes:
      secondary:
        - 10f863
  changes:
    - 5.9.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Fan
  search:
    terms: []
  styles:
    - solid
  unicode: f863
  voted: true
fantasy-flight-games:
  changes:
    - 5.4.0
  label: Fantasy Flight-games
  search:
    terms: []
  styles:
    - brands
  unicode: f6dc
  voted: false
faucet:
  aliases:
    unicodes:
      secondary:
        - '10e005'
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Faucet
  search:
    terms: []
  styles:
    - solid
  unicode: e005
  voted: false
faucet-drip:
  aliases:
    unicodes:
      composite:
        - 1f6b0
      secondary:
        - '10e006'
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Faucet Drip
  search:
    terms: []
  styles:
    - solid
  unicode: e006
  voted: false
fax:
  aliases:
    unicodes:
      composite:
        - 1f4e0
        - 1f5b7
      secondary:
        - 10f1ac
  changes:
    - 4.1.0
    - 5.0.0
    - 5.3.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Fax
  search:
    terms: []
  styles:
    - solid
  unicode: f1ac
  voted: false
feather:
  aliases:
    unicodes:
      composite:
        - 1fab6
      secondary:
        - 10f52d
  changes:
    - 5.0.13
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Feather
  search:
    terms: []
  styles:
    - solid
  unicode: f52d
  voted: true
feather-pointed:
  aliases:
    names:
      - feather-alt
    unicodes:
      secondary:
        - 10f56b
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Feather pointed
  search:
    terms: []
  styles:
    - solid
  unicode: f56b
  voted: true
fedex:
  changes:
    - 5.6.0
  label: FedEx
  search:
    terms: []
  styles:
    - brands
  unicode: f797
  voted: false
fedora:
  changes:
    - 5.6.0
    - 5.6.3
    - 5.8.0
    - 6.0.0
  label: Fedora
  search:
    terms: []
  styles:
    - brands
  unicode: f798
  voted: true
ferry:
  changes:
    - 6.1.0
    - 6.2.0
  label: Ferry
  search:
    terms: []
  styles:
    - solid
  unicode: e4ea
  voted: false
figma:
  changes:
    - 5.6.0
    - 5.7.0
    - 5.8.0
    - 5.15.4
    - 6.0.0-beta2
  label: Figma
  search:
    terms: []
  styles:
    - brands
  unicode: f799
  voted: false
file:
  aliases:
    unicodes:
      composite:
        - 1f4c4
        - 1f5cb
        - f016
      secondary:
        - 10f15b
  changes:
    - 3.2.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f15b
  voted: false
file-arrow-down:
  aliases:
    names:
      - file-download
    unicodes:
      secondary:
        - 10f56d
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File arrow down
  search:
    terms: []
  styles:
    - solid
  unicode: f56d
  voted: true
file-arrow-up:
  aliases:
    names:
      - file-upload
    unicodes:
      secondary:
        - 10f574
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File arrow up
  search:
    terms: []
  styles:
    - solid
  unicode: f574
  voted: true
file-audio:
  aliases:
    unicodes:
      secondary:
        - 10f1c7
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Audio File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c7
  voted: false
file-circle-check:
  changes:
    - 6.0.0
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: File Circle-Check
  search:
    terms: []
  styles:
    - solid
  unicode: e5a0
  voted: false
file-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: File Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e4eb
  voted: false
file-circle-minus:
  changes:
    - 6.1.0
    - 6.2.0
  label: File Circle-minus
  search:
    terms: []
  styles:
    - solid
  unicode: e4ed
  voted: false
file-circle-plus:
  aliases:
    unicodes:
      composite:
        - e4ee
  changes:
    - 6.0.0
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: File Circle-plus
  search:
    terms: []
  styles:
    - solid
  unicode: e494
  voted: false
file-circle-question:
  changes:
    - 6.1.0
    - 6.2.0
  label: File Circle-question
  search:
    terms: []
  styles:
    - solid
  unicode: e4ef
  voted: false
file-circle-xmark:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: File Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e5a1
  voted: false
file-code:
  aliases:
    unicodes:
      secondary:
        - 10f1c9
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Code File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c9
  voted: false
file-contract:
  aliases:
    unicodes:
      secondary:
        - 10f56c
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File Contract
  search:
    terms: []
  styles:
    - solid
  unicode: f56c
  voted: false
file-csv:
  aliases:
    unicodes:
      secondary:
        - 10f6dd
  changes:
    - 5.4.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: File CSV
  search:
    terms: []
  styles:
    - solid
  unicode: f6dd
  voted: false
file-excel:
  aliases:
    unicodes:
      secondary:
        - 10f1c3
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Excel File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c3
  voted: false
file-export:
  aliases:
    names:
      - arrow-right-from-file
    unicodes:
      secondary:
        - 10f56e
  changes:
    - 5.1.0
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File Export
  search:
    terms: []
  styles:
    - solid
  unicode: f56e
  voted: true
file-image:
  aliases:
    unicodes:
      composite:
        - 1f5bb
      secondary:
        - 10f1c5
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Image File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c5
  voted: false
file-import:
  aliases:
    names:
      - arrow-right-to-file
    unicodes:
      secondary:
        - 10f56f
  changes:
    - 5.1.0
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File Import
  search:
    terms: []
  styles:
    - solid
  unicode: f56f
  voted: true
file-invoice:
  aliases:
    unicodes:
      secondary:
        - 10f570
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File Invoice
  search:
    terms: []
  styles:
    - solid
  unicode: f570
  voted: true
file-invoice-dollar:
  aliases:
    unicodes:
      secondary:
        - 10f571
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File Invoice with US Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f571
  voted: true
file-lines:
  aliases:
    names:
      - file-alt
      - file-text
    unicodes:
      composite:
        - 1f5b9
        - 1f5ce
        - f0f6
      secondary:
        - 10f15c
  changes:
    - 3.2.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File lines
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f15c
  voted: false
file-medical:
  aliases:
    unicodes:
      secondary:
        - 10f477
  changes:
    - 5.0.7
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Medical File
  search:
    terms: []
  styles:
    - solid
  unicode: f477
  voted: false
file-pdf:
  aliases:
    unicodes:
      secondary:
        - 10f1c1
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: PDF File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c1
  voted: false
file-pen:
  aliases:
    names:
      - file-edit
    unicodes:
      composite:
        - 1f4dd
      secondary:
        - 10f31c
  changes:
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: File pen
  search:
    terms: []
  styles:
    - solid
  unicode: f31c
  voted: false
file-powerpoint:
  aliases:
    unicodes:
      secondary:
        - 10f1c4
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Powerpoint File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c4
  voted: false
file-prescription:
  aliases:
    unicodes:
      secondary:
        - 10f572
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File Prescription
  search:
    terms: []
  styles:
    - solid
  unicode: f572
  voted: false
file-shield:
  changes:
    - 6.1.0
    - 6.2.0
  label: File Shield
  search:
    terms: []
  styles:
    - solid
  unicode: e4f0
  voted: false
file-signature:
  aliases:
    unicodes:
      secondary:
        - 10f573
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File Signature
  search:
    terms: []
  styles:
    - solid
  unicode: f573
  voted: true
file-video:
  aliases:
    unicodes:
      secondary:
        - 10f1c8
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Video File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c8
  voted: false
file-waveform:
  aliases:
    names:
      - file-medical-alt
    unicodes:
      secondary:
        - 10f478
  changes:
    - 5.0.7
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File waveform
  search:
    terms: []
  styles:
    - solid
  unicode: f478
  voted: false
file-word:
  aliases:
    unicodes:
      secondary:
        - 10f1c2
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Word File
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c2
  voted: false
file-zipper:
  aliases:
    names:
      - file-archive
    unicodes:
      secondary:
        - 10f1c6
  changes:
    - 4.1.0
    - 5.0.0
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: File zipper
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1c6
  voted: false
fill:
  aliases:
    unicodes:
      secondary:
        - 10f575
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Fill
  search:
    terms: []
  styles:
    - solid
  unicode: f575
  voted: false
fill-drip:
  aliases:
    unicodes:
      secondary:
        - 10f576
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Fill Drip
  search:
    terms: []
  styles:
    - solid
  unicode: f576
  voted: false
film:
  aliases:
    unicodes:
      composite:
        - 1f39e
      secondary:
        - 10f008
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Film
  search:
    terms: []
  styles:
    - solid
  unicode: f008
  voted: false
filter:
  aliases:
    unicodes:
      secondary:
        - 10f0b0
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Filter
  search:
    terms: []
  styles:
    - solid
  unicode: f0b0
  voted: false
filter-circle-dollar:
  aliases:
    names:
      - funnel-dollar
    unicodes:
      secondary:
        - 10f662
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Filter Circle Dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f662
  voted: false
filter-circle-xmark:
  changes:
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Filter Circle X Mark
  search:
    terms: []
  styles:
    - solid
  unicode: e17b
  voted: true
fingerprint:
  aliases:
    unicodes:
      secondary:
        - 10f577
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Fingerprint
  search:
    terms: []
  styles:
    - solid
  unicode: f577
  voted: true
fire:
  aliases:
    unicodes:
      composite:
        - 1f525
      secondary:
        - 10f06d
  changes:
    - 1.0.0
    - 5.0.0
    - 5.6.0
    - 5.6.3
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: fire
  search:
    terms: []
  styles:
    - solid
  unicode: f06d
  voted: false
fire-burner:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Fire Burner
  search:
    terms: []
  styles:
    - solid
  unicode: e4f1
  voted: false
fire-extinguisher:
  aliases:
    unicodes:
      composite:
        - 1f9ef
      secondary:
        - 10f134
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: fire-extinguisher
  search:
    terms: []
  styles:
    - solid
  unicode: f134
  voted: false
fire-flame-curved:
  aliases:
    names:
      - fire-alt
    unicodes:
      secondary:
        - 10f7e4
  changes:
    - 5.6.3
    - 6.0.0-beta1
    - 6.2.0
  label: Fire flame curved
  search:
    terms: []
  styles:
    - solid
  unicode: f7e4
  voted: false
fire-flame-simple:
  aliases:
    names:
      - burn
    unicodes:
      secondary:
        - 10f46a
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Fire flame simple
  search:
    terms: []
  styles:
    - solid
  unicode: f46a
  voted: false
firefox:
  changes:
    - 4.4.0
    - 5.0.0
    - 5.0.1
    - 5.12.0
  label: Firefox
  search:
    terms: []
  styles:
    - brands
  unicode: f269
  voted: false
firefox-browser:
  changes:
    - 5.12.0
    - 5.14.0
  label: Firefox Browser
  search:
    terms: []
  styles:
    - brands
  unicode: e007
  voted: false
first-order:
  changes:
    - 4.6.0
    - 5.0.0
  label: First Order
  search:
    terms: []
  styles:
    - brands
  unicode: f2b0
  voted: false
first-order-alt:
  changes:
    - 5.0.12
  label: Alternate First Order
  search:
    terms: []
  styles:
    - brands
  unicode: f50a
  voted: false
firstdraft:
  changes:
    - 5.0.0
  label: firstdraft
  search:
    terms: []
  styles:
    - brands
  unicode: f3a1
  voted: false
fish:
  aliases:
    unicodes:
      composite:
        - 1f41f
      secondary:
        - 10f578
  changes:
    - 5.1.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Fish
  search:
    terms: []
  styles:
    - solid
  unicode: f578
  voted: true
fish-fins:
  changes:
    - 6.1.0
    - 6.2.0
  label: Fish Fins
  search:
    terms: []
  styles:
    - solid
  unicode: e4f2
  voted: false
flag:
  aliases:
    unicodes:
      composite:
        - 1f3f4
        - f11d
      secondary:
        - 10f024
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: flag
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f024
  voted: false
flag-checkered:
  aliases:
    unicodes:
      composite:
        - 1f3c1
      secondary:
        - 10f11e
  changes:
    - 3.1.0
    - 5.0.0
    - 5.7.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: flag-checkered
  search:
    terms: []
  styles:
    - solid
  unicode: f11e
  voted: false
flag-usa:
  aliases:
    unicodes:
      secondary:
        - 10f74d
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: United States of America Flag
  search:
    terms: []
  styles:
    - solid
  unicode: f74d
  voted: false
flask:
  aliases:
    unicodes:
      secondary:
        - 10f0c3
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Flask
  search:
    terms: []
  styles:
    - solid
  unicode: f0c3
  voted: false
flask-vial:
  changes:
    - 6.1.0
    - 6.2.0
  label: Flask and Vial
  search:
    terms: []
  styles:
    - solid
  unicode: e4f3
  voted: false
flickr:
  changes:
    - 3.2.0
    - 5.0.0
  label: Flickr
  search:
    terms: []
  styles:
    - brands
  unicode: f16e
  voted: false
flipboard:
  changes:
    - 5.0.5
    - 5.0.9
  label: Flipboard
  search:
    terms: []
  styles:
    - brands
  unicode: f44d
  voted: true
floppy-disk:
  aliases:
    names:
      - save
    unicodes:
      composite:
        - 1f4be
        - 1f5aa
      secondary:
        - 10f0c7
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Floppy Disk
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0c7
  voted: false
florin-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Florin Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e184
  voted: false
fly:
  changes:
    - 5.0.0
  label: Fly
  search:
    terms: []
  styles:
    - brands
  unicode: f417
  voted: false
folder:
  aliases:
    names:
      - folder-blank
    unicodes:
      composite:
        - 1f4c1
        - 1f5bf
        - f114
      secondary:
        - 10f07b
  changes:
    - 1.0.0
    - 5.0.0
    - 5.3.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Folder
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f07b
  voted: false
folder-closed:
  changes:
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Folder Closed
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: e185
  voted: false
folder-minus:
  aliases:
    unicodes:
      secondary:
        - 10f65d
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Folder Minus
  search:
    terms: []
  styles:
    - solid
  unicode: f65d
  voted: false
folder-open:
  aliases:
    unicodes:
      composite:
        - 1f4c2
        - 1f5c1
        - f115
      secondary:
        - 10f07c
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Folder Open
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f07c
  voted: false
folder-plus:
  aliases:
    unicodes:
      secondary:
        - 10f65e
  changes:
    - 5.3.0
    - 5.11.0
    - 5.12.1
    - 6.0.0-beta1
    - 6.2.0
  label: Folder Plus
  search:
    terms: []
  styles:
    - solid
  unicode: f65e
  voted: false
folder-tree:
  aliases:
    unicodes:
      secondary:
        - 10f802
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Folder Tree
  search:
    terms: []
  styles:
    - solid
  unicode: f802
  voted: true
font:
  aliases:
    unicodes:
      secondary:
        - 10f031
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: font
  search:
    terms: []
  styles:
    - solid
  unicode: f031
  voted: false
font-awesome:
  aliases:
    names:
      - font-awesome-flag
      - font-awesome-logo-full
    unicodes:
      composite:
        - f425
        - f4e6
      primary:
        - f4e6
      secondary:
        - 10f2b4
        - 10f4e6
  changes:
    - 4.6.0
    - 5.0.0
    - 5.15.4
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Font Awesome
  search:
    terms: []
  styles:
    - solid
    - regular
    - brands
  unicode: f2b4
  voted: false
fonticons:
  changes:
    - 4.4.0
    - 5.0.0
  label: Fonticons
  search:
    terms: []
  styles:
    - brands
  unicode: f280
  voted: false
fonticons-fi:
  changes:
    - 5.0.0
  label: Fonticons Fi
  search:
    terms: []
  styles:
    - brands
  unicode: f3a2
  voted: false
football:
  aliases:
    names:
      - football-ball
    unicodes:
      composite:
        - 1f3c8
      secondary:
        - 10f44e
  changes:
    - 5.0.5
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Football Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f44e
  voted: false
fort-awesome:
  changes:
    - 4.5.0
    - 5.0.0
    - 5.0.3
  label: Fort Awesome
  search:
    terms: []
  styles:
    - brands
  unicode: f286
  voted: false
fort-awesome-alt:
  changes:
    - 5.0.0
  label: Alternate Fort Awesome
  search:
    terms: []
  styles:
    - brands
  unicode: f3a3
  voted: false
forumbee:
  changes:
    - 4.3.0
    - 5.0.0
  label: Forumbee
  search:
    terms: []
  styles:
    - brands
  unicode: f211
  voted: false
forward:
  aliases:
    unicodes:
      composite:
        - '23e9'
      secondary:
        - 10f04e
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: forward
  search:
    terms: []
  styles:
    - solid
  unicode: f04e
  voted: false
forward-fast:
  aliases:
    names:
      - fast-forward
    unicodes:
      composite:
        - 23ed
      secondary:
        - 10f050
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Forward fast
  search:
    terms: []
  styles:
    - solid
  unicode: f050
  voted: false
forward-step:
  aliases:
    names:
      - step-forward
    unicodes:
      secondary:
        - 10f051
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Forward step
  search:
    terms: []
  styles:
    - solid
  unicode: f051
  voted: false
foursquare:
  changes:
    - 3.2.0
    - 5.0.0
  label: Foursquare
  search:
    terms: []
  styles:
    - brands
  unicode: f180
  voted: false
franc-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Franc Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e18f
  voted: false
free-code-camp:
  changes:
    - 4.7.0
    - 5.0.0
    - 5.12.0
  label: freeCodeCamp
  search:
    terms: []
  styles:
    - brands
  unicode: f2c5
  voted: false
freebsd:
  changes:
    - 5.0.0
  label: FreeBSD
  search:
    terms: []
  styles:
    - brands
  unicode: f3a4
  voted: false
frog:
  aliases:
    unicodes:
      secondary:
        - 10f52e
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Frog
  search:
    terms: []
  styles:
    - solid
  unicode: f52e
  voted: false
fulcrum:
  changes:
    - 5.0.12
    - 5.8.0
  label: Fulcrum
  search:
    terms: []
  styles:
    - brands
  unicode: f50b
  voted: false
futbol:
  aliases:
    names:
      - futbol-ball
      - soccer-ball
    unicodes:
      composite:
        - 26bd
      secondary:
        - 10f1e3
  changes:
    - 4.2.0
    - 5.0.0
    - 5.0.5
    - 6.0.0-beta1
    - 6.2.0
  label: Futbol ball
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1e3
  voted: false
g:
  aliases:
    unicodes:
      composite:
        - '67'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: G
  search:
    terms: []
  styles:
    - solid
  unicode: '47'
  voted: false
galactic-republic:
  changes:
    - 5.0.12
  label: Galactic Republic
  search:
    terms: []
  styles:
    - brands
  unicode: f50c
  voted: false
galactic-senate:
  changes:
    - 5.0.12
  label: Galactic Senate
  search:
    terms: []
  styles:
    - brands
  unicode: f50d
  voted: false
gamepad:
  aliases:
    unicodes:
      secondary:
        - 10f11b
  changes:
    - 3.1.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Gamepad
  search:
    terms: []
  styles:
    - solid
  unicode: f11b
  voted: false
gas-pump:
  aliases:
    unicodes:
      composite:
        - 26fd
      secondary:
        - 10f52f
  changes:
    - 5.0.13
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Gas Pump
  search:
    terms: []
  styles:
    - solid
  unicode: f52f
  voted: true
gauge:
  aliases:
    names:
      - dashboard
      - gauge-med
      - tachometer-alt-average
    unicodes:
      secondary:
        - 10f624
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.0.0
    - 6.2.0
  label: Gauge med
  search:
    terms: []
  styles:
    - solid
  unicode: f624
  voted: false
gauge-high:
  aliases:
    names:
      - tachometer-alt
      - tachometer-alt-fast
    unicodes:
      composite:
        - f3fd
      primary:
        - f3fd
      secondary:
        - 10f3fd
        - 10f625
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.0.0
    - 6.2.0
  label: Gauge
  search:
    terms: []
  styles:
    - solid
  unicode: f625
  voted: false
gauge-simple:
  aliases:
    names:
      - gauge-simple-med
      - tachometer-average
    unicodes:
      secondary:
        - 10f629
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.0.0
    - 6.2.0
  label: Gauge simple med
  search:
    terms: []
  styles:
    - solid
  unicode: f629
  voted: false
gauge-simple-high:
  aliases:
    names:
      - tachometer
      - tachometer-fast
    unicodes:
      composite:
        - f0e4
      primary:
        - f0e4
      secondary:
        - 10f0e4
        - 10f62a
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.0.0
    - 6.2.0
  label: Gauge simple
  search:
    terms: []
  styles:
    - solid
  unicode: f62a
  voted: false
gavel:
  aliases:
    names:
      - legal
    unicodes:
      secondary:
        - 10f0e3
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Gavel
  search:
    terms: []
  styles:
    - solid
  unicode: f0e3
  voted: false
gear:
  aliases:
    names:
      - cog
    unicodes:
      composite:
        - '2699'
      secondary:
        - 10f013
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Gear
  search:
    terms: []
  styles:
    - solid
  unicode: f013
  voted: false
gears:
  aliases:
    names:
      - cogs
    unicodes:
      secondary:
        - 10f085
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Gears
  search:
    terms: []
  styles:
    - solid
  unicode: f085
  voted: false
gem:
  aliases:
    unicodes:
      composite:
        - 1f48e
      secondary:
        - 10f3a5
  changes:
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Gem
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f3a5
  voted: false
genderless:
  aliases:
    unicodes:
      secondary:
        - 10f22d
  changes:
    - 4.4.0
    - 5.0.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Genderless
  search:
    terms: []
  styles:
    - solid
  unicode: f22d
  voted: false
get-pocket:
  changes:
    - 4.4.0
    - 5.0.0
  label: Get Pocket
  search:
    terms: []
  styles:
    - brands
  unicode: f265
  voted: false
gg:
  changes:
    - 4.4.0
    - 5.0.0
  label: GG Currency
  search:
    terms: []
  styles:
    - brands
  unicode: f260
  voted: false
gg-circle:
  changes:
    - 4.4.0
    - 5.0.0
  label: GG Currency Circle
  search:
    terms: []
  styles:
    - brands
  unicode: f261
  voted: false
ghost:
  aliases:
    unicodes:
      composite:
        - 1f47b
      secondary:
        - 10f6e2
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Ghost
  search:
    terms: []
  styles:
    - solid
  unicode: f6e2
  voted: false
gift:
  aliases:
    unicodes:
      composite:
        - 1f381
      secondary:
        - 10f06b
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.9
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: gift
  search:
    terms: []
  styles:
    - solid
  unicode: f06b
  voted: false
gifts:
  aliases:
    unicodes:
      secondary:
        - 10f79c
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Gifts
  search:
    terms: []
  styles:
    - solid
  unicode: f79c
  voted: false
git:
  changes:
    - 4.1.0
    - 5.0.0
    - 5.8.2
  label: Git
  search:
    terms: []
  styles:
    - brands
  unicode: f1d3
  voted: false
git-alt:
  changes:
    - 5.8.2
  label: Git Alt
  search:
    terms: []
  styles:
    - brands
  unicode: f841
  voted: false
github:
  changes:
    - 2.0.0
    - 5.0.0
  label: GitHub
  search:
    terms: []
  styles:
    - brands
  unicode: f09b
  voted: false
github-alt:
  changes:
    - 3.0.0
    - 5.0.0
  label: Alternate GitHub
  search:
    terms: []
  styles:
    - brands
  unicode: f113
  voted: false
gitkraken:
  changes:
    - 5.0.0
  label: GitKraken
  search:
    terms: []
  styles:
    - brands
  unicode: f3a6
  voted: false
gitlab:
  changes:
    - 4.6.0
    - 5.0.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.1.2
  label: GitLab
  search:
    terms: []
  styles:
    - brands
  unicode: f296
  voted: false
gitter:
  changes:
    - 5.0.0
  label: Gitter
  search:
    terms: []
  styles:
    - brands
  unicode: f426
  voted: false
glass-water:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Glass Water
  search:
    terms: []
  styles:
    - solid
  unicode: e4f4
  voted: false
glass-water-droplet:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Glass Water-droplet
  search:
    terms: []
  styles:
    - solid
  unicode: e4f5
  voted: false
glasses:
  aliases:
    unicodes:
      secondary:
        - 10f530
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Glasses
  search:
    terms: []
  styles:
    - solid
  unicode: f530
  voted: true
glide:
  changes:
    - 4.6.0
    - 5.0.0
  label: Glide
  search:
    terms: []
  styles:
    - brands
  unicode: f2a5
  voted: false
glide-g:
  changes:
    - 4.6.0
    - 5.0.0
  label: Glide G
  search:
    terms: []
  styles:
    - brands
  unicode: f2a6
  voted: false
globe:
  aliases:
    unicodes:
      composite:
        - 1f310
      secondary:
        - 10f0ac
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.9
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Globe
  search:
    terms: []
  styles:
    - solid
  unicode: f0ac
  voted: false
gofore:
  changes:
    - 5.0.0
  label: Gofore
  search:
    terms: []
  styles:
    - brands
  unicode: f3a7
  voted: false
golang:
  changes:
    - 6.0.0-beta2
  label: Go
  search:
    terms: []
  styles:
    - brands
  unicode: e40f
  voted: true
golf-ball-tee:
  aliases:
    names:
      - golf-ball
    unicodes:
      secondary:
        - 10f450
  changes:
    - 5.0.5
    - 6.0.0-beta1
    - 6.2.0
  label: Golf ball tee
  search:
    terms: []
  styles:
    - solid
  unicode: f450
  voted: false
goodreads:
  changes:
    - 5.0.0
  label: Goodreads
  search:
    terms: []
  styles:
    - brands
  unicode: f3a8
  voted: false
goodreads-g:
  changes:
    - 5.0.0
  label: Goodreads G
  search:
    terms: []
  styles:
    - brands
  unicode: f3a9
  voted: false
google:
  changes:
    - 4.1.0
    - 5.0.0
  label: Google Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a0
  voted: false
google-drive:
  changes:
    - 5.0.0
  label: Google Drive
  search:
    terms: []
  styles:
    - brands
  unicode: f3aa
  voted: false
google-pay:
  changes:
    - 5.13.1
    - 5.14.0
  label: Google Pay
  search:
    terms: []
  styles:
    - brands
  unicode: e079
  voted: false
google-play:
  changes:
    - 5.0.0
  label: Google Play
  search:
    terms: []
  styles:
    - brands
  unicode: f3ab
  voted: false
google-plus:
  changes:
    - 4.6.0
    - 5.0.0
    - 5.13.1
  label: Google Plus
  search:
    terms: []
  styles:
    - brands
  unicode: f2b3
  voted: false
google-plus-g:
  changes:
    - 2.0.0
    - 5.0.0
  label: Google Plus G
  search:
    terms: []
  styles:
    - brands
  unicode: f0d5
  voted: false
google-wallet:
  changes:
    - 4.2.0
    - 5.0.0
  label: Google Wallet
  search:
    terms: []
  styles:
    - brands
  unicode: f1ee
  voted: false
gopuram:
  aliases:
    unicodes:
      secondary:
        - 10f664
  changes:
    - 5.3.0
    - 5.7.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Gopuram
  search:
    terms: []
  styles:
    - solid
  unicode: f664
  voted: false
graduation-cap:
  aliases:
    names:
      - mortar-board
    unicodes:
      composite:
        - 1f393
      secondary:
        - 10f19d
  changes:
    - 4.1.0
    - 5.0.0
    - 5.2.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Graduation Cap
  search:
    terms: []
  styles:
    - solid
  unicode: f19d
  voted: false
gratipay:
  changes:
    - 3.2.0
    - 5.0.0
  label: Gratipay (Gittip)
  search:
    terms: []
  styles:
    - brands
  unicode: f184
  voted: false
grav:
  changes:
    - 4.7.0
    - 5.0.0
  label: Grav
  search:
    terms: []
  styles:
    - brands
  unicode: f2d6
  voted: false
greater-than:
  aliases:
    unicodes:
      composite:
        - f531
      primary:
        - f531
      secondary:
        - 103e
        - 10f531
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Greater Than
  search:
    terms: []
  styles:
    - solid
  unicode: 3e
  voted: true
greater-than-equal:
  aliases:
    unicodes:
      secondary:
        - 10f532
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Greater Than Equal To
  search:
    terms: []
  styles:
    - solid
  unicode: f532
  voted: true
grip:
  aliases:
    names:
      - grip-horizontal
    unicodes:
      secondary:
        - 10f58d
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Grip
  search:
    terms: []
  styles:
    - solid
  unicode: f58d
  voted: true
grip-lines:
  aliases:
    unicodes:
      secondary:
        - 10f7a4
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Grip Lines
  search:
    terms: []
  styles:
    - solid
  unicode: f7a4
  voted: true
grip-lines-vertical:
  aliases:
    unicodes:
      secondary:
        - 10f7a5
  changes:
    - 5.6.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Grip Lines Vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f7a5
  voted: true
grip-vertical:
  aliases:
    unicodes:
      secondary:
        - 10f58e
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Grip Vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f58e
  voted: true
gripfire:
  changes:
    - 5.0.0
  label: 'Gripfire, Inc.'
  search:
    terms: []
  styles:
    - brands
  unicode: f3ac
  voted: false
group-arrows-rotate:
  changes:
    - 6.1.0
    - 6.2.0
  label: Group Arrows-rotate
  search:
    terms: []
  styles:
    - solid
  unicode: e4f6
  voted: false
grunt:
  changes:
    - 5.0.0
  label: Grunt
  search:
    terms: []
  styles:
    - brands
  unicode: f3ad
  voted: false
guarani-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Guarani Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e19a
  voted: false
guilded:
  changes:
    - 5.15.0
  label: Guilded
  search:
    terms: []
  styles:
    - brands
  unicode: e07e
  voted: false
guitar:
  aliases:
    unicodes:
      secondary:
        - 10f7a6
  changes:
    - 5.6.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Guitar
  search:
    terms: []
  styles:
    - solid
  unicode: f7a6
  voted: true
gulp:
  changes:
    - 5.0.0
  label: Gulp
  search:
    terms: []
  styles:
    - brands
  unicode: f3ae
  voted: false
gun:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Gun
  search:
    terms: []
  styles:
    - solid
  unicode: e19b
  voted: true
h:
  aliases:
    unicodes:
      composite:
        - '68'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: H
  search:
    terms: []
  styles:
    - solid
  unicode: '48'
  voted: false
hacker-news:
  changes:
    - 4.1.0
    - 5.0.0
  label: Hacker News
  search:
    terms: []
  styles:
    - brands
  unicode: f1d4
  voted: false
hackerrank:
  changes:
    - 5.2.0
    - 5.8.0
  label: Hackerrank
  search:
    terms: []
  styles:
    - brands
  unicode: f5f7
  voted: true
hammer:
  aliases:
    unicodes:
      composite:
        - 1f528
      secondary:
        - 10f6e3
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hammer
  search:
    terms: []
  styles:
    - solid
  unicode: f6e3
  voted: true
hamsa:
  aliases:
    unicodes:
      secondary:
        - 10f665
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hamsa
  search:
    terms: []
  styles:
    - solid
  unicode: f665
  voted: false
hand:
  aliases:
    names:
      - hand-paper
    unicodes:
      composite:
        - 1f91a
        - 270b
      secondary:
        - 10f256
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Paper (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f256
  voted: false
hand-back-fist:
  aliases:
    names:
      - hand-rock
    unicodes:
      secondary:
        - 10f255
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Rock (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f255
  voted: false
hand-dots:
  aliases:
    names:
      - allergies
    unicodes:
      secondary:
        - 10f461
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Hand dots
  search:
    terms: []
  styles:
    - solid
  unicode: f461
  voted: false
hand-fist:
  aliases:
    names:
      - fist-raised
    unicodes:
      composite:
        - 270a
      secondary:
        - 10f6de
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Raised Fist
  search:
    terms: []
  styles:
    - solid
  unicode: f6de
  voted: false
hand-holding:
  aliases:
    unicodes:
      secondary:
        - 10f4bd
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Holding
  search:
    terms: []
  styles:
    - solid
  unicode: f4bd
  voted: false
hand-holding-dollar:
  aliases:
    names:
      - hand-holding-usd
    unicodes:
      secondary:
        - 10f4c0
  changes:
    - 5.0.9
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand holding dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f4c0
  voted: false
hand-holding-droplet:
  aliases:
    names:
      - hand-holding-water
    unicodes:
      secondary:
        - 10f4c1
  changes:
    - 5.0.9
    - 5.13.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand holding droplet
  search:
    terms: []
  styles:
    - solid
  unicode: f4c1
  voted: false
hand-holding-hand:
  changes:
    - 6.1.0
    - 6.2.0
  label: Hand Holding-hand
  search:
    terms: []
  styles:
    - solid
  unicode: e4f7
  voted: false
hand-holding-heart:
  aliases:
    unicodes:
      secondary:
        - 10f4be
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Holding Heart
  search:
    terms: []
  styles:
    - solid
  unicode: f4be
  voted: false
hand-holding-medical:
  aliases:
    unicodes:
      secondary:
        - 10e05c
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Holding Medical Cross
  search:
    terms: []
  styles:
    - solid
  unicode: e05c
  voted: false
hand-lizard:
  aliases:
    unicodes:
      secondary:
        - 10f258
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Lizard (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f258
  voted: false
hand-middle-finger:
  aliases:
    unicodes:
      composite:
        - 1f595
      secondary:
        - 10f806
  changes:
    - 5.7.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Hand with Middle Finger Raised
  search:
    terms: []
  styles:
    - solid
  unicode: f806
  voted: true
hand-peace:
  aliases:
    unicodes:
      composite:
        - 270c
      secondary:
        - 10f25b
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Peace (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f25b
  voted: false
hand-point-down:
  aliases:
    unicodes:
      secondary:
        - 10f0a7
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Pointing Down
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0a7
  voted: false
hand-point-left:
  aliases:
    unicodes:
      secondary:
        - 10f0a5
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Pointing Left
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0a5
  voted: false
hand-point-right:
  aliases:
    unicodes:
      secondary:
        - 10f0a4
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Pointing Right
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0a4
  voted: false
hand-point-up:
  aliases:
    unicodes:
      composite:
        - 261d
      secondary:
        - 10f0a6
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Pointing Up
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0a6
  voted: false
hand-pointer:
  aliases:
    unicodes:
      secondary:
        - 10f25a
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Pointer (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f25a
  voted: false
hand-scissors:
  aliases:
    unicodes:
      secondary:
        - 10f257
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Scissors (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f257
  voted: false
hand-sparkles:
  aliases:
    unicodes:
      secondary:
        - 10e05d
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hand Sparkles
  search:
    terms: []
  styles:
    - solid
  unicode: e05d
  voted: false
hand-spock:
  aliases:
    unicodes:
      composite:
        - 1f596
      secondary:
        - 10f259
  changes:
    - 4.4.0
    - 5.0.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Spock (Hand)
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f259
  voted: false
handcuffs:
  changes:
    - 6.1.0
    - 6.2.0
  label: Handcuffs
  search:
    terms: []
  styles:
    - solid
  unicode: e4f8
  voted: false
hands:
  aliases:
    names:
      - sign-language
      - signing
    unicodes:
      secondary:
        - 10f2a7
  changes:
    - 4.6.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Hands
  search:
    terms: []
  styles:
    - solid
  unicode: f2a7
  voted: false
hands-asl-interpreting:
  aliases:
    names:
      - american-sign-language-interpreting
      - asl-interpreting
      - hands-american-sign-language-interpreting
    unicodes:
      secondary:
        - 10f2a3
  changes:
    - 4.6.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hands american sign language interpreting
  search:
    terms: []
  styles:
    - solid
  unicode: f2a3
  voted: false
hands-bound:
  changes:
    - 6.1.0
    - 6.2.0
  label: Hands Bound
  search:
    terms: []
  styles:
    - solid
  unicode: e4f9
  voted: false
hands-bubbles:
  aliases:
    names:
      - hands-wash
    unicodes:
      secondary:
        - 10e05e
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hands bubbles
  search:
    terms: []
  styles:
    - solid
  unicode: e05e
  voted: false
hands-clapping:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Hands Clapping
  search:
    terms: []
  styles:
    - solid
  unicode: e1a8
  voted: false
hands-holding:
  aliases:
    unicodes:
      secondary:
        - 10f4c2
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Hands holding
  search:
    terms: []
  styles:
    - solid
  unicode: f4c2
  voted: false
hands-holding-child:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Hands Holding-child
  search:
    terms: []
  styles:
    - solid
  unicode: e4fa
  voted: false
hands-holding-circle:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Hands Holding-circle
  search:
    terms: []
  styles:
    - solid
  unicode: e4fb
  voted: false
hands-praying:
  aliases:
    names:
      - praying-hands
    unicodes:
      secondary:
        - 10f684
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hands praying
  search:
    terms: []
  styles:
    - solid
  unicode: f684
  voted: false
handshake:
  aliases:
    unicodes:
      secondary:
        - 10f2b5
  changes:
    - 4.7.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Handshake
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2b5
  voted: false
handshake-angle:
  aliases:
    names:
      - hands-helping
    unicodes:
      secondary:
        - 10f4c4
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Handshake angle
  search:
    terms: []
  styles:
    - solid
  unicode: f4c4
  voted: false
handshake-simple:
  aliases:
    names:
      - handshake-alt
    unicodes:
      composite:
        - 1f91d
      secondary:
        - 10f4c6
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Handshake simple
  search:
    terms: []
  styles:
    - solid
  unicode: f4c6
  voted: false
handshake-simple-slash:
  aliases:
    names:
      - handshake-alt-slash
    unicodes:
      secondary:
        - 10e05f
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Handshake simple slash
  search:
    terms: []
  styles:
    - solid
  unicode: e05f
  voted: false
handshake-slash:
  aliases:
    unicodes:
      secondary:
        - '10e060'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Handshake Slash
  search:
    terms: []
  styles:
    - solid
  unicode: e060
  voted: false
hanukiah:
  aliases:
    unicodes:
      composite:
        - 1f54e
      secondary:
        - 10f6e6
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hanukiah
  search:
    terms: []
  styles:
    - solid
  unicode: f6e6
  voted: false
hard-drive:
  aliases:
    names:
      - hdd
    unicodes:
      composite:
        - 1f5b4
      secondary:
        - 10f0a0
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Hard drive
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0a0
  voted: false
hashnode:
  changes:
    - 6.0.0
  label: Hashnode
  search:
    terms: []
  styles:
    - brands
  unicode: e499
  voted: false
hashtag:
  aliases:
    unicodes:
      composite:
        - f292
      primary:
        - f292
      secondary:
        - '1023'
        - 10f292
  changes:
    - 4.5.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hashtag
  search:
    terms: []
  styles:
    - solid
  unicode: '23'
  voted: false
hat-cowboy:
  aliases:
    unicodes:
      secondary:
        - 10f8c0
  changes:
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cowboy Hat
  search:
    terms: []
  styles:
    - solid
  unicode: f8c0
  voted: false
hat-cowboy-side:
  aliases:
    unicodes:
      secondary:
        - 10f8c1
  changes:
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cowboy Hat Side
  search:
    terms: []
  styles:
    - solid
  unicode: f8c1
  voted: false
hat-wizard:
  aliases:
    unicodes:
      secondary:
        - 10f6e8
  changes:
    - 5.4.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Wizard's Hat
  search:
    terms: []
  styles:
    - solid
  unicode: f6e8
  voted: false
head-side-cough:
  aliases:
    unicodes:
      secondary:
        - '10e061'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Head Side Cough
  search:
    terms: []
  styles:
    - solid
  unicode: e061
  voted: false
head-side-cough-slash:
  aliases:
    unicodes:
      secondary:
        - '10e062'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Head Side-cough-slash
  search:
    terms: []
  styles:
    - solid
  unicode: e062
  voted: false
head-side-mask:
  aliases:
    unicodes:
      secondary:
        - '10e063'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Head Side Mask
  search:
    terms: []
  styles:
    - solid
  unicode: e063
  voted: false
head-side-virus:
  aliases:
    unicodes:
      secondary:
        - '10e064'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Head Side Virus
  search:
    terms: []
  styles:
    - solid
  unicode: e064
  voted: false
heading:
  aliases:
    names:
      - header
    unicodes:
      secondary:
        - 10f1dc
  changes:
    - 4.1.0
    - 5.0.0
    - 5.9.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: heading
  search:
    terms: []
  styles:
    - solid
  unicode: f1dc
  voted: false
headphones:
  aliases:
    unicodes:
      composite:
        - 1f3a7
      secondary:
        - 10f025
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: headphones
  search:
    terms: []
  styles:
    - solid
  unicode: f025
  voted: false
headphones-simple:
  aliases:
    names:
      - headphones-alt
    unicodes:
      secondary:
        - 10f58f
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Headphones simple
  search:
    terms: []
  styles:
    - solid
  unicode: f58f
  voted: true
headset:
  aliases:
    unicodes:
      secondary:
        - 10f590
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Headset
  search:
    terms: []
  styles:
    - solid
  unicode: f590
  voted: true
heart:
  aliases:
    unicodes:
      composite:
        - 1f499
        - 1f49a
        - 1f49b
        - 1f49c
        - 1f5a4
        - 1f90d
        - 1f90e
        - 1f9e1
        - '2665'
        - '2764'
        - f08a
      secondary:
        - 10f004
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.9
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Heart
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f004
  voted: false
heart-circle-bolt:
  changes:
    - 6.1.0
    - 6.2.0
  label: Heart Circle-bolt
  search:
    terms: []
  styles:
    - solid
  unicode: e4fc
  voted: false
heart-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Heart Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e4fd
  voted: false
heart-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Heart Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e4fe
  voted: false
heart-circle-minus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Heart Circle-minus
  search:
    terms: []
  styles:
    - solid
  unicode: e4ff
  voted: false
heart-circle-plus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Heart Circle-plus
  search:
    terms: []
  styles:
    - solid
  unicode: e500
  voted: false
heart-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Heart Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e501
  voted: false
heart-crack:
  aliases:
    names:
      - heart-broken
    unicodes:
      composite:
        - 1f494
      secondary:
        - 10f7a9
  changes:
    - 5.6.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Heart crack
  search:
    terms: []
  styles:
    - solid
  unicode: f7a9
  voted: true
heart-pulse:
  aliases:
    names:
      - heartbeat
    unicodes:
      secondary:
        - 10f21e
  changes:
    - 4.3.0
    - 5.0.0
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Heart pulse
  search:
    terms: []
  styles:
    - solid
  unicode: f21e
  voted: false
helicopter:
  aliases:
    unicodes:
      composite:
        - 1f681
      secondary:
        - 10f533
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Helicopter
  search:
    terms: []
  styles:
    - solid
  unicode: f533
  voted: true
helicopter-symbol:
  changes:
    - 6.1.0
    - 6.2.0
  label: Helicopter Symbol
  search:
    terms: []
  styles:
    - solid
  unicode: e502
  voted: false
helmet-safety:
  aliases:
    names:
      - hard-hat
      - hat-hard
    unicodes:
      secondary:
        - 10f807
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Helmet safety
  search:
    terms: []
  styles:
    - solid
  unicode: f807
  voted: true
helmet-un:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Helmet Un
  search:
    terms: []
  styles:
    - solid
  unicode: e503
  voted: false
highlighter:
  aliases:
    unicodes:
      secondary:
        - 10f591
  changes:
    - 5.1.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Highlighter
  search:
    terms: []
  styles:
    - solid
  unicode: f591
  voted: true
hill-avalanche:
  changes:
    - 6.1.0
    - 6.2.0
  label: Hill Avalanche
  search:
    terms: []
  styles:
    - solid
  unicode: e507
  voted: false
hill-rockslide:
  changes:
    - 6.1.0
    - 6.2.0
  label: Hill Rockslide
  search:
    terms: []
  styles:
    - solid
  unicode: e508
  voted: false
hippo:
  aliases:
    unicodes:
      composite:
        - 1f99b
      secondary:
        - 10f6ed
  changes:
    - 5.4.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Hippo
  search:
    terms: []
  styles:
    - solid
  unicode: f6ed
  voted: false
hips:
  changes:
    - 5.0.5
  label: Hips
  search:
    terms: []
  styles:
    - brands
  unicode: f452
  voted: false
hire-a-helper:
  changes:
    - 5.0.0
  label: HireAHelper
  search:
    terms: []
  styles:
    - brands
  unicode: f3b0
  voted: false
hive:
  changes:
    - 5.15.0
  label: Hive Blockchain Network
  search:
    terms: []
  styles:
    - brands
  unicode: e07f
  voted: false
hockey-puck:
  aliases:
    unicodes:
      secondary:
        - 10f453
  changes:
    - 5.0.5
    - 6.0.0-beta1
    - 6.2.0
  label: Hockey Puck
  search:
    terms: []
  styles:
    - solid
  unicode: f453
  voted: false
holly-berry:
  aliases:
    unicodes:
      secondary:
        - 10f7aa
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Holly Berry
  search:
    terms: []
  styles:
    - solid
  unicode: f7aa
  voted: false
hooli:
  changes:
    - 5.0.0
    - 5.7.0
  label: Hooli
  search:
    terms: []
  styles:
    - brands
  unicode: f427
  voted: false
hornbill:
  changes:
    - 5.1.0
    - 5.8.0
  label: Hornbill
  search:
    terms: []
  styles:
    - brands
  unicode: f592
  voted: false
horse:
  aliases:
    unicodes:
      composite:
        - 1f40e
      secondary:
        - 10f6f0
  changes:
    - 5.4.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Horse
  search:
    terms: []
  styles:
    - solid
  unicode: f6f0
  voted: false
horse-head:
  aliases:
    unicodes:
      secondary:
        - 10f7ab
  changes:
    - 5.6.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Horse Head
  search:
    terms: []
  styles:
    - solid
  unicode: f7ab
  voted: false
hospital:
  aliases:
    names:
      - hospital-alt
      - hospital-wide
    unicodes:
      composite:
        - 1f3e5
        - f47d
      primary:
        - f47d
      secondary:
        - 10f0f8
        - 10f47d
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: hospital
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0f8
  voted: false
hospital-user:
  aliases:
    unicodes:
      secondary:
        - 10f80d
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Hospital with User
  search:
    terms: []
  styles:
    - solid
  unicode: f80d
  voted: false
hot-tub-person:
  aliases:
    names:
      - hot-tub
    unicodes:
      secondary:
        - 10f593
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hot tub person
  search:
    terms: []
  styles:
    - solid
  unicode: f593
  voted: false
hotdog:
  aliases:
    unicodes:
      composite:
        - 1f32d
      secondary:
        - 10f80f
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hot Dog
  search:
    terms: []
  styles:
    - solid
  unicode: f80f
  voted: false
hotel:
  aliases:
    unicodes:
      composite:
        - 1f3e8
      secondary:
        - 10f594
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Hotel
  search:
    terms: []
  styles:
    - solid
  unicode: f594
  voted: false
hotjar:
  changes:
    - 5.0.0
  label: Hotjar
  search:
    terms: []
  styles:
    - brands
  unicode: f3b1
  voted: false
hourglass:
  aliases:
    names:
      - hourglass-empty
    unicodes:
      composite:
        - 23f3
        - f250
      secondary:
        - 10f254
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.2
    - 6.2.0
  label: Hourglass
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f254
  voted: false
hourglass-end:
  aliases:
    names:
      - hourglass-3
    unicodes:
      composite:
        - 231b
      secondary:
        - 10f253
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Hourglass End
  search:
    terms: []
  styles:
    - solid
  unicode: f253
  voted: false
hourglass-half:
  aliases:
    names:
      - hourglass-2
    unicodes:
      secondary:
        - 10f252
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.2
    - 6.2.0
  label: Hourglass Half
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f252
  voted: false
hourglass-start:
  aliases:
    names:
      - hourglass-1
    unicodes:
      secondary:
        - 10f251
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Hourglass Start
  search:
    terms: []
  styles:
    - solid
  unicode: f251
  voted: false
house:
  aliases:
    names:
      - home
      - home-alt
      - home-lg-alt
    unicodes:
      composite:
        - 1f3e0
        - f80a
        - f80c
      primary:
        - f80a
        - f80c
      secondary:
        - 10f015
        - 10f80a
        - 10f80c
  changes:
    - 1.0.0
    - 5.0.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: House
  search:
    terms: []
  styles:
    - solid
  unicode: f015
  voted: false
house-chimney:
  aliases:
    names:
      - home-lg
    unicodes:
      composite:
        - f80b
      primary:
        - f80b
      secondary:
        - 10f80b
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: House Chimney
  search:
    terms: []
  styles:
    - solid
  unicode: e3af
  voted: false
house-chimney-crack:
  aliases:
    names:
      - house-damage
    unicodes:
      secondary:
        - 10f6f1
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: House crack
  search:
    terms: []
  styles:
    - solid
  unicode: f6f1
  voted: false
house-chimney-medical:
  aliases:
    names:
      - clinic-medical
    unicodes:
      secondary:
        - 10f7f2
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: House medical
  search:
    terms: []
  styles:
    - solid
  unicode: f7f2
  voted: false
house-chimney-user:
  aliases:
    unicodes:
      secondary:
        - '10e065'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: House User
  search:
    terms: []
  styles:
    - solid
  unicode: e065
  voted: false
house-chimney-window:
  aliases:
    unicodes:
      secondary:
        - 10e00d
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: House with Window + Chimney
  search:
    terms: []
  styles:
    - solid
  unicode: e00d
  voted: false
house-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e509
  voted: false
house-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e50a
  voted: false
house-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e50b
  voted: false
house-crack:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: House Simple Crack
  search:
    terms: []
  styles:
    - solid
  unicode: e3b1
  voted: false
house-fire:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Fire
  search:
    terms: []
  styles:
    - solid
  unicode: e50c
  voted: false
house-flag:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Flag
  search:
    terms: []
  styles:
    - solid
  unicode: e50d
  voted: false
house-flood-water:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Flood
  search:
    terms: []
  styles:
    - solid
  unicode: e50e
  voted: false
house-flood-water-circle-arrow-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Flood-circle-arrow-right
  search:
    terms: []
  styles:
    - solid
  unicode: e50f
  voted: false
house-laptop:
  aliases:
    names:
      - laptop-house
    unicodes:
      secondary:
        - '10e066'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: House laptop
  search:
    terms: []
  styles:
    - solid
  unicode: e066
  voted: false
house-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e510
  voted: false
house-medical:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: House Simple Medical
  search:
    terms: []
  styles:
    - solid
  unicode: e3b2
  voted: false
house-medical-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Medical-circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e511
  voted: false
house-medical-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Medical-circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e512
  voted: false
house-medical-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Medical-circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e513
  voted: false
house-medical-flag:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Medical-flag
  search:
    terms: []
  styles:
    - solid
  unicode: e514
  voted: false
house-signal:
  aliases:
    unicodes:
      secondary:
        - '10e012'
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: House Signal
  search:
    terms: []
  styles:
    - solid
  unicode: e012
  voted: false
house-tsunami:
  changes:
    - 6.1.0
    - 6.2.0
  label: House Tsunami
  search:
    terms: []
  styles:
    - solid
  unicode: e515
  voted: false
house-user:
  aliases:
    names:
      - home-user
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Home User
  search:
    terms: []
  styles:
    - solid
  unicode: e1b0
  voted: false
houzz:
  changes:
    - 4.4.0
    - 5.0.0
    - 5.0.9
    - 5.8.0
  label: Houzz
  search:
    terms: []
  styles:
    - brands
  unicode: f27c
  voted: false
hryvnia-sign:
  aliases:
    names:
      - hryvnia
    unicodes:
      composite:
        - 20b4
      secondary:
        - 10f6f2
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Hryvnia sign
  search:
    terms: []
  styles:
    - solid
  unicode: f6f2
  voted: true
html5:
  changes:
    - 3.1.0
    - 5.0.0
  label: HTML 5 Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f13b
  voted: false
hubspot:
  changes:
    - 5.0.0
  label: HubSpot
  search:
    terms: []
  styles:
    - brands
  unicode: f3b2
  voted: false
hurricane:
  aliases:
    unicodes:
      secondary:
        - 10f751
  changes:
    - 5.5.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Hurricane
  search:
    terms: []
  styles:
    - solid
  unicode: f751
  voted: false
i:
  aliases:
    unicodes:
      composite:
        - '69'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: I
  search:
    terms: []
  styles:
    - solid
  unicode: '49'
  voted: false
i-cursor:
  aliases:
    unicodes:
      secondary:
        - 10f246
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: I Beam Cursor
  search:
    terms: []
  styles:
    - solid
  unicode: f246
  voted: false
ice-cream:
  aliases:
    unicodes:
      composite:
        - 1f368
      secondary:
        - 10f810
  changes:
    - 5.7.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Ice Cream
  search:
    terms: []
  styles:
    - solid
  unicode: f810
  voted: false
icicles:
  aliases:
    unicodes:
      secondary:
        - 10f7ad
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Icicles
  search:
    terms: []
  styles:
    - solid
  unicode: f7ad
  voted: false
icons:
  aliases:
    names:
      - heart-music-camera-bolt
    unicodes:
      secondary:
        - 10f86d
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Icons
  search:
    terms: []
  styles:
    - solid
  unicode: f86d
  voted: false
id-badge:
  aliases:
    unicodes:
      secondary:
        - 10f2c1
  changes:
    - 4.7.0
    - 5.0.0
    - 5.0.3
    - 6.0.0-beta1
    - 6.2.0
  label: Identification Badge
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2c1
  voted: false
id-card:
  aliases:
    names:
      - drivers-license
    unicodes:
      composite:
        - f2c3
      secondary:
        - 10f2c2
  changes:
    - 4.7.0
    - 5.0.0
    - 5.0.3
    - 5.8.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Identification Card
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2c2
  voted: false
id-card-clip:
  aliases:
    names:
      - id-card-alt
    unicodes:
      secondary:
        - 10f47f
  changes:
    - 5.0.7
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Id card clip
  search:
    terms: []
  styles:
    - solid
  unicode: f47f
  voted: false
ideal:
  changes:
    - 5.12.0
    - 5.14.0
  label: iDeal
  search:
    terms: []
  styles:
    - brands
  unicode: e013
  voted: true
igloo:
  aliases:
    unicodes:
      secondary:
        - 10f7ae
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Igloo
  search:
    terms: []
  styles:
    - solid
  unicode: f7ae
  voted: false
image:
  aliases:
    unicodes:
      secondary:
        - 10f03e
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Image
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f03e
  voted: false
image-portrait:
  aliases:
    names:
      - portrait
    unicodes:
      secondary:
        - 10f3e0
  changes:
    - 5.0.0
    - 5.0.3
    - 6.0.0-beta1
    - 6.2.0
  label: Image portrait
  search:
    terms: []
  styles:
    - solid
  unicode: f3e0
  voted: false
images:
  aliases:
    unicodes:
      secondary:
        - 10f302
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Images
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f302
  voted: false
imdb:
  changes:
    - 4.7.0
    - 5.0.0
    - 6.1.2
  label: IMDB
  search:
    terms: []
  styles:
    - brands
  unicode: f2d8
  voted: false
inbox:
  aliases:
    unicodes:
      secondary:
        - 10f01c
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: inbox
  search:
    terms: []
  styles:
    - solid
  unicode: f01c
  voted: false
indent:
  aliases:
    unicodes:
      secondary:
        - 10f03c
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Indent
  search:
    terms: []
  styles:
    - solid
  unicode: f03c
  voted: false
indian-rupee-sign:
  aliases:
    names:
      - indian-rupee
      - inr
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Indian Rupee-sign
  search:
    terms: []
  styles:
    - solid
  unicode: e1bc
  voted: false
industry:
  aliases:
    unicodes:
      secondary:
        - 10f275
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Industry
  search:
    terms: []
  styles:
    - solid
  unicode: f275
  voted: false
infinity:
  aliases:
    unicodes:
      composite:
        - 221e
        - 267e
      secondary:
        - 10f534
  changes:
    - 5.0.13
    - 5.3.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Infinity
  search:
    terms: []
  styles:
    - solid
  unicode: f534
  voted: true
info:
  aliases:
    unicodes:
      secondary:
        - 10f129
  changes:
    - 3.1.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Info
  search:
    terms: []
  styles:
    - solid
  unicode: f129
  voted: false
instagram:
  changes:
    - 4.6.0
    - 5.0.0
  label: Instagram
  search:
    terms: []
  styles:
    - brands
  unicode: f16d
  voted: false
instalod:
  changes:
    - 5.15.0
  label: InstaLOD
  search:
    terms: []
  styles:
    - brands
  unicode: e081
  voted: false
intercom:
  changes:
    - 5.6.0
  label: Intercom
  search:
    terms: []
  styles:
    - brands
  unicode: f7af
  voted: false
internet-explorer:
  changes:
    - 4.4.0
    - 5.0.0
  label: Internet-explorer
  search:
    terms: []
  styles:
    - brands
  unicode: f26b
  voted: false
invision:
  changes:
    - 5.6.0
  label: InVision
  search:
    terms: []
  styles:
    - brands
  unicode: f7b0
  voted: false
ioxhost:
  changes:
    - 4.2.0
    - 5.0.0
  label: ioxhost
  search:
    terms: []
  styles:
    - brands
  unicode: f208
  voted: false
italic:
  aliases:
    unicodes:
      secondary:
        - 10f033
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: italic
  search:
    terms: []
  styles:
    - solid
  unicode: f033
  voted: false
itch-io:
  changes:
    - 5.8.0
  label: itch.io
  search:
    terms: []
  styles:
    - brands
  unicode: f83a
  voted: false
itunes:
  changes:
    - 5.0.0
  label: iTunes
  search:
    terms: []
  styles:
    - brands
  unicode: f3b4
  voted: false
itunes-note:
  changes:
    - 5.0.0
  label: Itunes Note
  search:
    terms: []
  styles:
    - brands
  unicode: f3b5
  voted: false
j:
  aliases:
    unicodes:
      composite:
        - 6a
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: J
  search:
    terms: []
  styles:
    - solid
  unicode: 4a
  voted: false
jar:
  changes:
    - 6.1.0
    - 6.2.0
  label: Jar
  search:
    terms: []
  styles:
    - solid
  unicode: e516
  voted: false
jar-wheat:
  changes:
    - 6.1.0
    - 6.2.0
  label: Jar Wheat
  search:
    terms: []
  styles:
    - solid
  unicode: e517
  voted: false
java:
  changes:
    - 5.0.10
    - 5.7.0
    - 5.8.0
  label: Java
  search:
    terms: []
  styles:
    - brands
  unicode: f4e4
  voted: false
jedi:
  aliases:
    unicodes:
      secondary:
        - 10f669
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Jedi
  search:
    terms: []
  styles:
    - solid
  unicode: f669
  voted: false
jedi-order:
  changes:
    - 5.0.12
    - 5.7.0
  label: Jedi Order
  search:
    terms: []
  styles:
    - brands
  unicode: f50e
  voted: false
jenkins:
  changes:
    - 5.0.0
  label: Jenkis
  search:
    terms: []
  styles:
    - brands
  unicode: f3b6
  voted: false
jet-fighter:
  aliases:
    names:
      - fighter-jet
    unicodes:
      secondary:
        - 10f0fb
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Jet fighter
  search:
    terms: []
  styles:
    - solid
  unicode: f0fb
  voted: false
jet-fighter-up:
  changes:
    - 6.1.0
    - 6.2.0
  label: Jet Fighter Up
  search:
    terms: []
  styles:
    - solid
  unicode: e518
  voted: false
jira:
  changes:
    - 5.6.0
  label: Jira
  search:
    terms: []
  styles:
    - brands
  unicode: f7b1
  voted: true
joget:
  changes:
    - 5.0.0
  label: Joget
  search:
    terms: []
  styles:
    - brands
  unicode: f3b7
  voted: false
joint:
  aliases:
    unicodes:
      secondary:
        - 10f595
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Joint
  search:
    terms: []
  styles:
    - solid
  unicode: f595
  voted: false
joomla:
  changes:
    - 4.1.0
    - 5.0.0
  label: Joomla Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1aa
  voted: false
js:
  changes:
    - 5.0.0
  label: JavaScript (JS)
  search:
    terms: []
  styles:
    - brands
  unicode: f3b8
  voted: false
jsfiddle:
  changes:
    - 4.1.0
    - 5.0.0
  label: jsFiddle
  search:
    terms: []
  styles:
    - brands
  unicode: f1cc
  voted: false
jug-detergent:
  changes:
    - 6.1.0
    - 6.2.0
  label: Jug Detergent
  search:
    terms: []
  styles:
    - solid
  unicode: e519
  voted: false
k:
  aliases:
    unicodes:
      composite:
        - 6b
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: K
  search:
    terms: []
  styles:
    - solid
  unicode: 4b
  voted: false
kaaba:
  aliases:
    unicodes:
      composite:
        - 1f54b
      secondary:
        - 10f66b
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.1.2
    - 6.2.0
  label: Kaaba
  search:
    terms: []
  styles:
    - solid
  unicode: f66b
  voted: false
kaggle:
  changes:
    - 5.2.0
    - 5.8.0
  label: Kaggle
  search:
    terms: []
  styles:
    - brands
  unicode: f5fa
  voted: true
key:
  aliases:
    unicodes:
      composite:
        - 1f511
      secondary:
        - 10f084
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: key
  search:
    terms: []
  styles:
    - solid
  unicode: f084
  voted: false
keybase:
  changes:
    - 5.0.11
    - 5.8.0
    - 5.10.2
    - 5.11.0
  label: Keybase
  search:
    terms: []
  styles:
    - brands
  unicode: f4f5
  voted: true
keyboard:
  aliases:
    unicodes:
      composite:
        - '2328'
      secondary:
        - 10f11c
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Keyboard
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f11c
  voted: false
keycdn:
  changes:
    - 5.0.0
  label: KeyCDN
  search:
    terms: []
  styles:
    - brands
  unicode: f3ba
  voted: false
khanda:
  aliases:
    unicodes:
      composite:
        - 262c
      secondary:
        - 10f66d
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Khanda
  search:
    terms: []
  styles:
    - solid
  unicode: f66d
  voted: false
kickstarter:
  changes:
    - 5.0.0
  label: Kickstarter
  search:
    terms: []
  styles:
    - brands
  unicode: f3bb
  voted: false
kickstarter-k:
  changes:
    - 5.0.0
  label: Kickstarter K
  search:
    terms: []
  styles:
    - brands
  unicode: f3bc
  voted: false
kip-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Kip Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e1c4
  voted: false
kit-medical:
  aliases:
    names:
      - first-aid
    unicodes:
      secondary:
        - 10f479
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Kit medical
  search:
    terms: []
  styles:
    - solid
  unicode: f479
  voted: false
kitchen-set:
  changes:
    - 6.1.0
    - 6.2.0
  label: Kitchen Set
  search:
    terms: []
  styles:
    - solid
  unicode: e51a
  voted: false
kiwi-bird:
  aliases:
    unicodes:
      secondary:
        - 10f535
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Kiwi Bird
  search:
    terms: []
  styles:
    - solid
  unicode: f535
  voted: false
korvue:
  changes:
    - 5.0.2
  label: KORVUE
  search:
    terms: []
  styles:
    - brands
  unicode: f42f
  voted: false
l:
  aliases:
    unicodes:
      composite:
        - 6c
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: L
  search:
    terms: []
  styles:
    - solid
  unicode: 4c
  voted: false
land-mine-on:
  changes:
    - 6.1.0
    - 6.2.0
  label: Land Mine-on
  search:
    terms: []
  styles:
    - solid
  unicode: e51b
  voted: false
landmark:
  aliases:
    unicodes:
      composite:
        - 1f3db
      secondary:
        - 10f66f
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Landmark
  search:
    terms: []
  styles:
    - solid
  unicode: f66f
  voted: false
landmark-dome:
  aliases:
    names:
      - landmark-alt
    unicodes:
      secondary:
        - 10f752
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Landmark dome
  search:
    terms: []
  styles:
    - solid
  unicode: f752
  voted: false
landmark-flag:
  changes:
    - 6.1.0
    - 6.2.0
  label: Landmark Flag
  search:
    terms: []
  styles:
    - solid
  unicode: e51c
  voted: false
language:
  aliases:
    unicodes:
      secondary:
        - 10f1ab
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Language
  search:
    terms: []
  styles:
    - solid
  unicode: f1ab
  voted: false
laptop:
  aliases:
    unicodes:
      composite:
        - 1f4bb
      secondary:
        - 10f109
  changes:
    - 3.0.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Laptop
  search:
    terms: []
  styles:
    - solid
  unicode: f109
  voted: false
laptop-code:
  aliases:
    unicodes:
      secondary:
        - 10f5fc
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Laptop Code
  search:
    terms: []
  styles:
    - solid
  unicode: f5fc
  voted: false
laptop-file:
  changes:
    - 6.1.0
    - 6.2.0
  label: Laptop File
  search:
    terms: []
  styles:
    - solid
  unicode: e51d
  voted: false
laptop-medical:
  aliases:
    unicodes:
      secondary:
        - 10f812
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Laptop Medical
  search:
    terms: []
  styles:
    - solid
  unicode: f812
  voted: false
laravel:
  changes:
    - 5.0.0
    - 5.0.3
    - 5.11.2
  label: Laravel
  search:
    terms: []
  styles:
    - brands
  unicode: f3bd
  voted: false
lari-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Lari Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e1c8
  voted: false
lastfm:
  changes:
    - 4.2.0
    - 5.0.0
  label: last.fm
  search:
    terms: []
  styles:
    - brands
  unicode: f202
  voted: false
layer-group:
  aliases:
    unicodes:
      secondary:
        - 10f5fd
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Layer Group
  search:
    terms: []
  styles:
    - solid
  unicode: f5fd
  voted: false
leaf:
  aliases:
    unicodes:
      secondary:
        - 10f06c
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: leaf
  search:
    terms: []
  styles:
    - solid
  unicode: f06c
  voted: false
leanpub:
  changes:
    - 4.3.0
    - 5.0.0
  label: Leanpub
  search:
    terms: []
  styles:
    - brands
  unicode: f212
  voted: false
left-long:
  aliases:
    names:
      - long-arrow-alt-left
    unicodes:
      secondary:
        - 10f30a
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Left long
  search:
    terms: []
  styles:
    - solid
  unicode: f30a
  voted: false
left-right:
  aliases:
    names:
      - arrows-alt-h
    unicodes:
      composite:
        - '2194'
      secondary:
        - 10f337
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Left right
  search:
    terms: []
  styles:
    - solid
  unicode: f337
  voted: false
lemon:
  aliases:
    unicodes:
      composite:
        - 1f34b
      secondary:
        - 10f094
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Lemon
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f094
  voted: false
less:
  changes:
    - 5.0.0
  label: Less
  search:
    terms: []
  styles:
    - brands
  unicode: f41d
  voted: false
less-than:
  aliases:
    unicodes:
      composite:
        - f536
      primary:
        - f536
      secondary:
        - 103c
        - 10f536
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Less Than
  search:
    terms: []
  styles:
    - solid
  unicode: 3c
  voted: true
less-than-equal:
  aliases:
    unicodes:
      secondary:
        - 10f537
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Less Than Equal To
  search:
    terms: []
  styles:
    - solid
  unicode: f537
  voted: true
life-ring:
  aliases:
    unicodes:
      secondary:
        - 10f1cd
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Life Ring
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1cd
  voted: false
lightbulb:
  aliases:
    unicodes:
      composite:
        - 1f4a1
      secondary:
        - 10f0eb
  changes:
    - 3.0.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Lightbulb
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0eb
  voted: false
line:
  changes:
    - 5.0.0
  label: Line
  search:
    terms: []
  styles:
    - brands
  unicode: f3c0
  voted: false
lines-leaning:
  changes:
    - 6.1.0
    - 6.2.0
  label: Lines Leaning
  search:
    terms: []
  styles:
    - solid
  unicode: e51e
  voted: false
link:
  aliases:
    names:
      - chain
    unicodes:
      composite:
        - 1f517
      secondary:
        - 10f0c1
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Link
  search:
    terms: []
  styles:
    - solid
  unicode: f0c1
  voted: false
link-slash:
  aliases:
    names:
      - chain-broken
      - chain-slash
      - unlink
    unicodes:
      secondary:
        - 10f127
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Link Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f127
  voted: false
linkedin:
  changes:
    - 1.0.0
    - 5.0.0
  label: LinkedIn
  search:
    terms: []
  styles:
    - brands
  unicode: f08c
  voted: false
linkedin-in:
  changes:
    - 2.0.0
    - 5.0.0
    - 5.4.1
    - 5.8.0
    - 5.8.1
  label: LinkedIn In
  search:
    terms: []
  styles:
    - brands
  unicode: f0e1
  voted: false
linode:
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
  label: Linode
  search:
    terms: []
  styles:
    - brands
  unicode: f2b8
  voted: false
linux:
  changes:
    - 3.2.0
    - 5.0.0
  label: Linux
  search:
    terms: []
  styles:
    - brands
  unicode: f17c
  voted: false
lira-sign:
  aliases:
    unicodes:
      composite:
        - 20a4
      secondary:
        - 10f195
  changes:
    - 4.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Turkish Lira Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f195
  voted: false
list:
  aliases:
    names:
      - list-squares
    unicodes:
      secondary:
        - 10f03a
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: List
  search:
    terms: []
  styles:
    - solid
  unicode: f03a
  voted: false
list-check:
  aliases:
    names:
      - tasks
    unicodes:
      secondary:
        - 10f0ae
  changes:
    - 2.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: List check
  search:
    terms: []
  styles:
    - solid
  unicode: f0ae
  voted: false
list-ol:
  aliases:
    names:
      - list-1-2
      - list-numeric
    unicodes:
      secondary:
        - 10f0cb
  changes:
    - 2.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: list-ol
  search:
    terms: []
  styles:
    - solid
  unicode: f0cb
  voted: false
list-ul:
  aliases:
    names:
      - list-dots
    unicodes:
      secondary:
        - 10f0ca
  changes:
    - 2.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: list-ul
  search:
    terms: []
  styles:
    - solid
  unicode: f0ca
  voted: false
litecoin-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Litecoin Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e1d3
  voted: true
location-arrow:
  aliases:
    unicodes:
      secondary:
        - 10f124
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: location-arrow
  search:
    terms: []
  styles:
    - solid
  unicode: f124
  voted: false
location-crosshairs:
  aliases:
    names:
      - location
    unicodes:
      secondary:
        - 10f601
  changes:
    - 5.2.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Location Crosshairs
  search:
    terms: []
  styles:
    - solid
  unicode: f601
  voted: false
location-dot:
  aliases:
    names:
      - map-marker-alt
    unicodes:
      secondary:
        - 10f3c5
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Location dot
  search:
    terms: []
  styles:
    - solid
  unicode: f3c5
  voted: false
location-pin:
  aliases:
    names:
      - map-marker
    unicodes:
      secondary:
        - 10f041
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Location
  search:
    terms: []
  styles:
    - solid
  unicode: f041
  voted: false
location-pin-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: Location Pin-lock
  search:
    terms: []
  styles:
    - solid
  unicode: e51f
  voted: false
lock:
  aliases:
    unicodes:
      composite:
        - 1f512
      secondary:
        - 10f023
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: lock
  search:
    terms: []
  styles:
    - solid
  unicode: f023
  voted: false
lock-open:
  aliases:
    unicodes:
      secondary:
        - 10f3c1
  changes:
    - 3.1.0
    - 5.0.0
    - 5.0.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Lock Open
  search:
    terms: []
  styles:
    - solid
  unicode: f3c1
  voted: false
locust:
  changes:
    - 6.1.0
    - 6.2.0
  label: Locust
  search:
    terms: []
  styles:
    - solid
  unicode: e520
  voted: false
lungs:
  aliases:
    unicodes:
      composite:
        - 1fac1
      secondary:
        - 10f604
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Lungs
  search:
    terms: []
  styles:
    - solid
  unicode: f604
  voted: false
lungs-virus:
  aliases:
    unicodes:
      secondary:
        - '10e067'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Lungs Virus
  search:
    terms: []
  styles:
    - solid
  unicode: e067
  voted: false
lyft:
  changes:
    - 5.0.0
  label: lyft
  search:
    terms: []
  styles:
    - brands
  unicode: f3c3
  voted: false
m:
  aliases:
    unicodes:
      composite:
        - 6d
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: M
  search:
    terms: []
  styles:
    - solid
  unicode: 4d
  voted: false
magento:
  changes:
    - 5.0.0
  label: Magento
  search:
    terms: []
  styles:
    - brands
  unicode: f3c4
  voted: false
magnet:
  aliases:
    unicodes:
      composite:
        - 1f9f2
      secondary:
        - 10f076
  changes:
    - 1.0.0
    - 5.0.0
    - 5.8.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: magnet
  search:
    terms: []
  styles:
    - solid
  unicode: f076
  voted: false
magnifying-glass:
  aliases:
    names:
      - search
    unicodes:
      composite:
        - 1f50d
      secondary:
        - 10f002
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Magnifying glass
  search:
    terms: []
  styles:
    - solid
  unicode: f002
  voted: false
magnifying-glass-arrow-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: Magnifying Glass-arrow-right
  search:
    terms: []
  styles:
    - solid
  unicode: e521
  voted: false
magnifying-glass-chart:
  changes:
    - 6.1.0
    - 6.2.0
  label: Magnifying Glass-chart
  search:
    terms: []
  styles:
    - solid
  unicode: e522
  voted: false
magnifying-glass-dollar:
  aliases:
    names:
      - search-dollar
    unicodes:
      secondary:
        - 10f688
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Magnifying glass dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f688
  voted: false
magnifying-glass-location:
  aliases:
    names:
      - search-location
    unicodes:
      secondary:
        - 10f689
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Magnifying glass location
  search:
    terms: []
  styles:
    - solid
  unicode: f689
  voted: false
magnifying-glass-minus:
  aliases:
    names:
      - search-minus
    unicodes:
      secondary:
        - 10f010
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Magnifying glass minus
  search:
    terms: []
  styles:
    - solid
  unicode: f010
  voted: false
magnifying-glass-plus:
  aliases:
    names:
      - search-plus
    unicodes:
      secondary:
        - 10f00e
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Magnifying glass plus
  search:
    terms: []
  styles:
    - solid
  unicode: f00e
  voted: false
mailchimp:
  changes:
    - 5.1.0
    - 5.7.0
    - 5.8.0
    - 5.8.2
  label: Mailchimp
  search:
    terms: []
  styles:
    - brands
  unicode: f59e
  voted: true
manat-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Manat Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e1d5
  voted: false
mandalorian:
  changes:
    - 5.0.12
    - 5.8.0
  label: Mandalorian
  search:
    terms: []
  styles:
    - brands
  unicode: f50f
  voted: false
map:
  aliases:
    unicodes:
      composite:
        - 1f5fa
        - f278
      secondary:
        - 10f279
  changes:
    - 4.4.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Map
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f279
  voted: false
map-location:
  aliases:
    names:
      - map-marked
    unicodes:
      secondary:
        - 10f59f
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Map location
  search:
    terms: []
  styles:
    - solid
  unicode: f59f
  voted: false
map-location-dot:
  aliases:
    names:
      - map-marked-alt
    unicodes:
      secondary:
        - 10f5a0
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Map location dot
  search:
    terms: []
  styles:
    - solid
  unicode: f5a0
  voted: false
map-pin:
  aliases:
    unicodes:
      composite:
        - 1f4cd
      secondary:
        - 10f276
  changes:
    - 4.4.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Map Pin
  search:
    terms: []
  styles:
    - solid
  unicode: f276
  voted: false
markdown:
  changes:
    - 5.2.0
    - 5.7.0
  label: Markdown
  search:
    terms: []
  styles:
    - brands
  unicode: f60f
  voted: true
marker:
  aliases:
    unicodes:
      secondary:
        - 10f5a1
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Marker
  search:
    terms: []
  styles:
    - solid
  unicode: f5a1
  voted: true
mars:
  aliases:
    unicodes:
      composite:
        - '2642'
      secondary:
        - 10f222
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mars
  search:
    terms: []
  styles:
    - solid
  unicode: f222
  voted: false
mars-and-venus:
  aliases:
    unicodes:
      composite:
        - 26a5
      secondary:
        - 10f224
  changes:
    - 4.3.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mars and Venus
  search:
    terms: []
  styles:
    - solid
  unicode: f224
  voted: false
mars-and-venus-burst:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mars and Venus Burst
  search:
    terms: []
  styles:
    - solid
  unicode: e523
  voted: false
mars-double:
  aliases:
    unicodes:
      composite:
        - 26a3
      secondary:
        - 10f227
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mars Double
  search:
    terms: []
  styles:
    - solid
  unicode: f227
  voted: false
mars-stroke:
  aliases:
    unicodes:
      composite:
        - 26a6
      secondary:
        - 10f229
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mars Stroke
  search:
    terms: []
  styles:
    - solid
  unicode: f229
  voted: false
mars-stroke-right:
  aliases:
    names:
      - mars-stroke-h
    unicodes:
      composite:
        - 26a9
      secondary:
        - 10f22b
  changes:
    - 4.3.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mars stroke right
  search:
    terms: []
  styles:
    - solid
  unicode: f22b
  voted: false
mars-stroke-up:
  aliases:
    names:
      - mars-stroke-v
    unicodes:
      composite:
        - 26a8
      secondary:
        - 10f22a
  changes:
    - 4.3.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mars stroke up
  search:
    terms: []
  styles:
    - solid
  unicode: f22a
  voted: false
martini-glass:
  aliases:
    names:
      - glass-martini-alt
    unicodes:
      composite:
        - 1f378
      secondary:
        - 10f57b
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Martini glass
  search:
    terms: []
  styles:
    - solid
  unicode: f57b
  voted: false
martini-glass-citrus:
  aliases:
    names:
      - cocktail
    unicodes:
      secondary:
        - 10f561
  changes:
    - 5.1.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Martini glass citrus
  search:
    terms: []
  styles:
    - solid
  unicode: f561
  voted: false
martini-glass-empty:
  aliases:
    names:
      - glass-martini
    unicodes:
      secondary:
        - 10f000
  changes:
    - 1.0.0
    - 5.0.0
    - 5.1.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Martini glass empty
  search:
    terms: []
  styles:
    - solid
  unicode: f000
  voted: false
mask:
  aliases:
    unicodes:
      secondary:
        - 10f6fa
  changes:
    - 5.4.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Mask
  search:
    terms: []
  styles:
    - solid
  unicode: f6fa
  voted: false
mask-face:
  changes:
    - 6.0.0-beta1
    - 6.0.0
    - 6.1.0
    - 6.2.0
  label: Face Mask
  search:
    terms: []
  styles:
    - solid
  unicode: e1d7
  voted: false
mask-ventilator:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mask Ventilator
  search:
    terms: []
  styles:
    - solid
  unicode: e524
  voted: false
masks-theater:
  aliases:
    names:
      - theater-masks
    unicodes:
      composite:
        - 1f3ad
      secondary:
        - 10f630
  changes:
    - 5.2.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Masks theater
  search:
    terms: []
  styles:
    - solid
  unicode: f630
  voted: false
mastodon:
  changes:
    - 5.0.11
    - 5.8.0
  label: Mastodon
  search:
    terms: []
  styles:
    - brands
  unicode: f4f6
  voted: true
mattress-pillow:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mattress Pillow
  search:
    terms: []
  styles:
    - solid
  unicode: e525
  voted: false
maxcdn:
  changes:
    - 3.1.0
    - 5.0.0
  label: MaxCDN
  search:
    terms: []
  styles:
    - brands
  unicode: f136
  voted: false
maximize:
  aliases:
    names:
      - expand-arrows-alt
    unicodes:
      secondary:
        - 10f31e
  changes:
    - 5.0.0
    - 5.8.0
    - 6.0.0-beta1
    - 6.2.0
  label: Maximize
  search:
    terms: []
  styles:
    - solid
  unicode: f31e
  voted: false
mdb:
  changes:
    - 5.11.0
  label: Material Design for Bootstrap
  search:
    terms: []
  styles:
    - brands
  unicode: f8ca
  voted: false
medal:
  aliases:
    unicodes:
      composite:
        - 1f3c5
      secondary:
        - 10f5a2
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Medal
  search:
    terms: []
  styles:
    - solid
  unicode: f5a2
  voted: true
medapps:
  changes:
    - 5.0.0
  label: MedApps
  search:
    terms: []
  styles:
    - brands
  unicode: f3c6
  voted: false
medium:
  aliases:
    names:
      - medium-m
    unicodes:
      composite:
        - f3c7
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
  label: Medium
  search:
    terms: []
  styles:
    - brands
  unicode: f23a
  voted: false
medrt:
  changes:
    - 5.0.0
  label: MRT
  search:
    terms: []
  styles:
    - brands
  unicode: f3c8
  voted: false
meetup:
  changes:
    - 4.7.0
    - 5.0.0
  label: Meetup
  search:
    terms: []
  styles:
    - brands
  unicode: f2e0
  voted: false
megaport:
  changes:
    - 5.1.0
  label: Megaport
  search:
    terms: []
  styles:
    - brands
  unicode: f5a3
  voted: false
memory:
  aliases:
    unicodes:
      secondary:
        - 10f538
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Memory
  search:
    terms: []
  styles:
    - solid
  unicode: f538
  voted: true
mendeley:
  changes:
    - 5.6.0
  label: Mendeley
  search:
    terms: []
  styles:
    - brands
  unicode: f7b3
  voted: true
menorah:
  aliases:
    unicodes:
      secondary:
        - 10f676
  changes:
    - 5.3.0
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Menorah
  search:
    terms: []
  styles:
    - solid
  unicode: f676
  voted: false
mercury:
  aliases:
    unicodes:
      composite:
        - 263f
      secondary:
        - 10f223
  changes:
    - 4.3.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mercury
  search:
    terms: []
  styles:
    - solid
  unicode: f223
  voted: false
message:
  aliases:
    names:
      - comment-alt
    unicodes:
      secondary:
        - 10f27a
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Message
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f27a
  voted: false
meta:
  changes:
    - 6.0.0
    - 6.1.2
  label: Meta
  search:
    terms: []
  styles:
    - brands
  unicode: e49b
  voted: false
meteor:
  aliases:
    unicodes:
      composite:
        - '2604'
      secondary:
        - 10f753
  changes:
    - 5.5.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Meteor
  search:
    terms: []
  styles:
    - solid
  unicode: f753
  voted: false
microblog:
  changes:
    - 5.12.0
    - 5.14.0
  label: Micro.blog
  search:
    terms: []
  styles:
    - brands
  unicode: e01a
  voted: true
microchip:
  aliases:
    unicodes:
      secondary:
        - 10f2db
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Microchip
  search:
    terms: []
  styles:
    - solid
  unicode: f2db
  voted: false
microphone:
  aliases:
    unicodes:
      secondary:
        - 10f130
  changes:
    - 3.1.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: microphone
  search:
    terms: []
  styles:
    - solid
  unicode: f130
  voted: false
microphone-lines:
  aliases:
    names:
      - microphone-alt
    unicodes:
      composite:
        - 1f399
      secondary:
        - 10f3c9
  changes:
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Microphone lines
  search:
    terms: []
  styles:
    - solid
  unicode: f3c9
  voted: false
microphone-lines-slash:
  aliases:
    names:
      - microphone-alt-slash
    unicodes:
      secondary:
        - 10f539
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Microphone lines slash
  search:
    terms: []
  styles:
    - solid
  unicode: f539
  voted: false
microphone-slash:
  aliases:
    unicodes:
      secondary:
        - 10f131
  changes:
    - 3.1.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Microphone Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f131
  voted: false
microscope:
  aliases:
    unicodes:
      composite:
        - 1f52c
      secondary:
        - 10f610
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Microscope
  search:
    terms: []
  styles:
    - solid
  unicode: f610
  voted: false
microsoft:
  changes:
    - 5.0.0
  label: Microsoft
  search:
    terms: []
  styles:
    - brands
  unicode: f3ca
  voted: true
mill-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Mill Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e1ed
  voted: false
minimize:
  aliases:
    names:
      - compress-arrows-alt
    unicodes:
      secondary:
        - 10f78c
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Minimize
  search:
    terms: []
  styles:
    - solid
  unicode: f78c
  voted: true
minus:
  aliases:
    names:
      - subtract
    unicodes:
      composite:
        - '2013'
        - '2212'
        - '2796'
      secondary:
        - 10f068
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: minus
  search:
    terms: []
  styles:
    - solid
  unicode: f068
  voted: false
mitten:
  aliases:
    unicodes:
      secondary:
        - 10f7b5
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mitten
  search:
    terms: []
  styles:
    - solid
  unicode: f7b5
  voted: false
mix:
  changes:
    - 5.0.0
    - 5.0.3
  label: Mix
  search:
    terms: []
  styles:
    - brands
  unicode: f3cb
  voted: false
mixcloud:
  changes:
    - 4.5.0
    - 5.0.0
    - 6.1.2
  label: Mixcloud
  search:
    terms: []
  styles:
    - brands
  unicode: f289
  voted: false
mixer:
  changes:
    - 5.12.1
    - 5.14.0
  label: Mixer
  search:
    terms: []
  styles:
    - brands
  unicode: e056
  voted: true
mizuni:
  changes:
    - 5.0.0
  label: Mizuni
  search:
    terms: []
  styles:
    - brands
  unicode: f3cc
  voted: false
mobile:
  aliases:
    names:
      - mobile-android
      - mobile-phone
    unicodes:
      composite:
        - 1f4f1
      secondary:
        - 10f3ce
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mobile
  search:
    terms: []
  styles:
    - solid
  unicode: f3ce
  voted: false
mobile-button:
  aliases:
    unicodes:
      secondary:
        - 10f10b
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mobile button
  search:
    terms: []
  styles:
    - solid
  unicode: f10b
  voted: false
mobile-retro:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mobile Retro
  search:
    terms: []
  styles:
    - solid
  unicode: e527
  voted: false
mobile-screen:
  aliases:
    names:
      - mobile-android-alt
    unicodes:
      secondary:
        - 10f3cf
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Mobile screen
  search:
    terms: []
  styles:
    - solid
  unicode: f3cf
  voted: false
mobile-screen-button:
  aliases:
    names:
      - mobile-alt
    unicodes:
      secondary:
        - 10f3cd
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mobile screen button
  search:
    terms: []
  styles:
    - solid
  unicode: f3cd
  voted: false
modx:
  changes:
    - 4.5.0
    - 5.0.0
  label: MODX
  search:
    terms: []
  styles:
    - brands
  unicode: f285
  voted: false
monero:
  changes:
    - 5.0.0
  label: Monero
  search:
    terms: []
  styles:
    - brands
  unicode: f3d0
  voted: false
money-bill:
  aliases:
    unicodes:
      secondary:
        - 10f0d6
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Money Bill
  search:
    terms: []
  styles:
    - solid
  unicode: f0d6
  voted: false
money-bill-1:
  aliases:
    names:
      - money-bill-alt
    unicodes:
      secondary:
        - 10f3d1
  changes:
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Money bill 1
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f3d1
  voted: false
money-bill-1-wave:
  aliases:
    names:
      - money-bill-wave-alt
    unicodes:
      secondary:
        - 10f53b
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Money bill 1 wave
  search:
    terms: []
  styles:
    - solid
  unicode: f53b
  voted: true
money-bill-transfer:
  changes:
    - 6.1.0
    - 6.2.0
  label: Money Bill-transfer
  search:
    terms: []
  styles:
    - solid
  unicode: e528
  voted: false
money-bill-trend-up:
  changes:
    - 6.1.0
    - 6.2.0
  label: Money Bill-trend-up
  search:
    terms: []
  styles:
    - solid
  unicode: e529
  voted: false
money-bill-wave:
  aliases:
    unicodes:
      secondary:
        - 10f53a
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Wavy Money Bill
  search:
    terms: []
  styles:
    - solid
  unicode: f53a
  voted: true
money-bill-wheat:
  changes:
    - 6.1.0
    - 6.2.0
  label: Money Bill-wheat
  search:
    terms: []
  styles:
    - solid
  unicode: e52a
  voted: false
money-bills:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Money Bills
  search:
    terms: []
  styles:
    - solid
  unicode: e1f3
  voted: false
money-check:
  aliases:
    unicodes:
      secondary:
        - 10f53c
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Money Check
  search:
    terms: []
  styles:
    - solid
  unicode: f53c
  voted: true
money-check-dollar:
  aliases:
    names:
      - money-check-alt
    unicodes:
      secondary:
        - 10f53d
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Money check dollar
  search:
    terms: []
  styles:
    - solid
  unicode: f53d
  voted: true
monument:
  aliases:
    unicodes:
      secondary:
        - 10f5a6
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Monument
  search:
    terms: []
  styles:
    - solid
  unicode: f5a6
  voted: false
moon:
  aliases:
    unicodes:
      composite:
        - 1f319
        - 23fe
      secondary:
        - 10f186
  changes:
    - 3.2.0
    - 5.0.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Moon
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f186
  voted: false
mortar-pestle:
  aliases:
    unicodes:
      secondary:
        - 10f5a7
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mortar Pestle
  search:
    terms: []
  styles:
    - solid
  unicode: f5a7
  voted: false
mosque:
  aliases:
    unicodes:
      composite:
        - 1f54c
      secondary:
        - 10f678
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Mosque
  search:
    terms: []
  styles:
    - solid
  unicode: f678
  voted: false
mosquito:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mosquito
  search:
    terms: []
  styles:
    - solid
  unicode: e52b
  voted: false
mosquito-net:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mosquito Net
  search:
    terms: []
  styles:
    - solid
  unicode: e52c
  voted: false
motorcycle:
  aliases:
    unicodes:
      composite:
        - 1f3cd
      secondary:
        - 10f21c
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Motorcycle
  search:
    terms: []
  styles:
    - solid
  unicode: f21c
  voted: false
mound:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mound
  search:
    terms: []
  styles:
    - solid
  unicode: e52d
  voted: false
mountain:
  aliases:
    unicodes:
      composite:
        - 1f3d4
      secondary:
        - 10f6fc
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mountain
  search:
    terms: []
  styles:
    - solid
  unicode: f6fc
  voted: false
mountain-city:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mountain City
  search:
    terms: []
  styles:
    - solid
  unicode: e52e
  voted: false
mountain-sun:
  changes:
    - 6.1.0
    - 6.2.0
  label: Mountain Sun
  search:
    terms: []
  styles:
    - solid
  unicode: e52f
  voted: false
mug-hot:
  aliases:
    unicodes:
      composite:
        - '2615'
      secondary:
        - 10f7b6
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mug Hot
  search:
    terms: []
  styles:
    - solid
  unicode: f7b6
  voted: false
mug-saucer:
  aliases:
    names:
      - coffee
    unicodes:
      secondary:
        - 10f0f4
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Mug saucer
  search:
    terms: []
  styles:
    - solid
  unicode: f0f4
  voted: false
music:
  aliases:
    unicodes:
      composite:
        - 1f3b5
      secondary:
        - 10f001
  changes:
    - 1.0.0
    - 5.0.0
    - 5.2.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Music
  search:
    terms: []
  styles:
    - solid
  unicode: f001
  voted: false
'n':
  aliases:
    unicodes:
      composite:
        - 6e
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: 'N'
  search:
    terms: []
  styles:
    - solid
  unicode: 4e
  voted: false
naira-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Naira Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e1f6
  voted: false
napster:
  changes:
    - 5.0.0
  label: Napster
  search:
    terms: []
  styles:
    - brands
  unicode: f3d2
  voted: false
neos:
  changes:
    - 5.2.0
    - 5.8.0
  label: Neos
  search:
    terms: []
  styles:
    - brands
  unicode: f612
  voted: true
network-wired:
  aliases:
    unicodes:
      secondary:
        - 10f6ff
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Wired Network
  search:
    terms: []
  styles:
    - solid
  unicode: f6ff
  voted: true
neuter:
  aliases:
    unicodes:
      composite:
        - 26b2
      secondary:
        - 10f22c
  changes:
    - 4.3.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Neuter
  search:
    terms: []
  styles:
    - solid
  unicode: f22c
  voted: false
newspaper:
  aliases:
    unicodes:
      composite:
        - 1f4f0
      secondary:
        - 10f1ea
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Newspaper
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1ea
  voted: false
nfc-directional:
  changes:
    - 6.1.0
  label: NFC Directional
  search:
    terms: []
  styles:
    - brands
  unicode: e530
  voted: false
nfc-symbol:
  changes:
    - 6.1.0
    - 6.2.0
  label: NFC Simplified
  search:
    terms: []
  styles:
    - brands
  unicode: e531
  voted: false
nimblr:
  changes:
    - 5.1.0
    - 5.8.0
  label: Nimblr
  search:
    terms: []
  styles:
    - brands
  unicode: f5a8
  voted: false
node:
  changes:
    - 5.0.0
  label: Node.js
  search:
    terms: []
  styles:
    - brands
  unicode: f419
  voted: true
node-js:
  changes:
    - 5.0.0
    - 5.0.3
  label: Node.js JS
  search:
    terms: []
  styles:
    - brands
  unicode: f3d3
  voted: false
not-equal:
  aliases:
    unicodes:
      secondary:
        - 10f53e
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Not Equal
  search:
    terms: []
  styles:
    - solid
  unicode: f53e
  voted: true
note-sticky:
  aliases:
    names:
      - sticky-note
    unicodes:
      composite:
        - f24a
      secondary:
        - 10f249
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Note sticky
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f249
  voted: false
notes-medical:
  aliases:
    unicodes:
      secondary:
        - 10f481
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Medical Notes
  search:
    terms: []
  styles:
    - solid
  unicode: f481
  voted: false
npm:
  changes:
    - 5.0.0
  label: npm
  search:
    terms: []
  styles:
    - brands
  unicode: f3d4
  voted: false
ns8:
  changes:
    - 5.0.0
    - 5.15.0
  label: NS8
  search:
    terms: []
  styles:
    - brands
  unicode: f3d5
  voted: false
nutritionix:
  changes:
    - 5.0.0
  label: Nutritionix
  search:
    terms: []
  styles:
    - brands
  unicode: f3d6
  voted: false
o:
  aliases:
    unicodes:
      composite:
        - 6f
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: O
  search:
    terms: []
  styles:
    - solid
  unicode: 4f
  voted: false
object-group:
  aliases:
    unicodes:
      secondary:
        - 10f247
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Object Group
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f247
  voted: false
object-ungroup:
  aliases:
    unicodes:
      secondary:
        - 10f248
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Object Ungroup
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f248
  voted: false
octopus-deploy:
  changes:
    - 5.15.0
  label: Octopus Deploy
  search:
    terms: []
  styles:
    - brands
  unicode: e082
  voted: false
odnoklassniki:
  changes:
    - 4.4.0
    - 5.0.0
  label: Odnoklassniki
  search:
    terms: []
  styles:
    - brands
  unicode: f263
  voted: false
oil-can:
  aliases:
    unicodes:
      secondary:
        - 10f613
  changes:
    - 5.2.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Oil Can
  search:
    terms: []
  styles:
    - solid
  unicode: f613
  voted: false
oil-well:
  changes:
    - 6.1.0
    - 6.2.0
  label: Oil Well
  search:
    terms: []
  styles:
    - solid
  unicode: e532
  voted: false
old-republic:
  changes:
    - 5.0.12
  label: Old Republic
  search:
    terms: []
  styles:
    - brands
  unicode: f510
  voted: false
om:
  aliases:
    unicodes:
      composite:
        - 1f549
      secondary:
        - 10f679
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Om
  search:
    terms: []
  styles:
    - solid
  unicode: f679
  voted: false
opencart:
  changes:
    - 4.4.0
    - 5.0.0
  label: OpenCart
  search:
    terms: []
  styles:
    - brands
  unicode: f23d
  voted: false
openid:
  changes:
    - 4.1.0
    - 5.0.0
  label: OpenID
  search:
    terms: []
  styles:
    - brands
  unicode: f19b
  voted: false
opera:
  changes:
    - 4.4.0
    - 5.0.0
  label: Opera
  search:
    terms: []
  styles:
    - brands
  unicode: f26a
  voted: false
optin-monster:
  changes:
    - 4.4.0
    - 5.0.0
    - 5.7.0
  label: Optin Monster
  search:
    terms: []
  styles:
    - brands
  unicode: f23c
  voted: false
orcid:
  changes:
    - 5.11.0
  label: ORCID
  search:
    terms: []
  styles:
    - brands
  unicode: f8d2
  voted: false
osi:
  changes:
    - 5.0.0
    - 5.8.0
  label: Open Source Initiative
  search:
    terms: []
  styles:
    - brands
  unicode: f41a
  voted: false
otter:
  aliases:
    unicodes:
      composite:
        - 1f9a6
      secondary:
        - 10f700
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Otter
  search:
    terms: []
  styles:
    - solid
  unicode: f700
  voted: false
outdent:
  aliases:
    names:
      - dedent
    unicodes:
      secondary:
        - 10f03b
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Outdent
  search:
    terms: []
  styles:
    - solid
  unicode: f03b
  voted: false
p:
  aliases:
    unicodes:
      composite:
        - '70'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: P
  search:
    terms: []
  styles:
    - solid
  unicode: '50'
  voted: false
padlet:
  changes:
    - 6.0.0
  label: Padlet
  search:
    terms: []
  styles:
    - brands
  unicode: e4a0
  voted: false
page4:
  changes:
    - 5.0.0
  label: page4 Corporation
  search:
    terms: []
  styles:
    - brands
  unicode: f3d7
  voted: false
pagelines:
  changes:
    - 4.0.0
    - 5.0.0
  label: Pagelines
  search:
    terms: []
  styles:
    - brands
  unicode: f18c
  voted: false
pager:
  aliases:
    unicodes:
      composite:
        - 1f4df
      secondary:
        - 10f815
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Pager
  search:
    terms: []
  styles:
    - solid
  unicode: f815
  voted: false
paint-roller:
  aliases:
    unicodes:
      secondary:
        - 10f5aa
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Paint Roller
  search:
    terms: []
  styles:
    - solid
  unicode: f5aa
  voted: false
paintbrush:
  aliases:
    names:
      - paint-brush
    unicodes:
      composite:
        - 1f58c
      secondary:
        - 10f1fc
  changes:
    - 4.2.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Paint Brush
  search:
    terms: []
  styles:
    - solid
  unicode: f1fc
  voted: false
palette:
  aliases:
    unicodes:
      composite:
        - 1f3a8
      secondary:
        - 10f53f
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Palette
  search:
    terms: []
  styles:
    - solid
  unicode: f53f
  voted: true
palfed:
  changes:
    - 5.0.0
    - 5.0.3
  label: Palfed
  search:
    terms: []
  styles:
    - brands
  unicode: f3d8
  voted: false
pallet:
  aliases:
    unicodes:
      secondary:
        - 10f482
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Pallet
  search:
    terms: []
  styles:
    - solid
  unicode: f482
  voted: false
panorama:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Panorama
  search:
    terms: []
  styles:
    - solid
  unicode: e209
  voted: false
paper-plane:
  aliases:
    unicodes:
      composite:
        - f1d9
      secondary:
        - 10f1d8
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Paper Plane
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f1d8
  voted: false
paperclip:
  aliases:
    unicodes:
      composite:
        - 1f4ce
      secondary:
        - 10f0c6
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Paperclip
  search:
    terms: []
  styles:
    - solid
  unicode: f0c6
  voted: false
parachute-box:
  aliases:
    unicodes:
      secondary:
        - 10f4cd
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Parachute Box
  search:
    terms: []
  styles:
    - solid
  unicode: f4cd
  voted: false
paragraph:
  aliases:
    unicodes:
      composite:
        - b6
      secondary:
        - 10f1dd
  changes:
    - 4.1.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: paragraph
  search:
    terms: []
  styles:
    - solid
  unicode: f1dd
  voted: false
passport:
  aliases:
    unicodes:
      secondary:
        - 10f5ab
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Passport
  search:
    terms: []
  styles:
    - solid
  unicode: f5ab
  voted: false
paste:
  aliases:
    names:
      - file-clipboard
    unicodes:
      secondary:
        - 10f0ea
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Paste
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0ea
  voted: false
patreon:
  changes:
    - 5.0.0
    - 5.0.3
  label: Patreon
  search:
    terms: []
  styles:
    - brands
  unicode: f3d9
  voted: false
pause:
  aliases:
    unicodes:
      composite:
        - 23f8
      secondary:
        - 10f04c
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: pause
  search:
    terms: []
  styles:
    - solid
  unicode: f04c
  voted: false
paw:
  aliases:
    unicodes:
      secondary:
        - 10f1b0
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Paw
  search:
    terms: []
  styles:
    - solid
  unicode: f1b0
  voted: false
paypal:
  changes:
    - 4.2.0
    - 5.0.0
  label: Paypal
  search:
    terms: []
  styles:
    - brands
  unicode: f1ed
  voted: false
peace:
  aliases:
    unicodes:
      composite:
        - 262e
      secondary:
        - 10f67c
  changes:
    - 5.3.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Peace
  search:
    terms: []
  styles:
    - solid
  unicode: f67c
  voted: false
pen:
  aliases:
    unicodes:
      composite:
        - 1f58a
      secondary:
        - 10f304
  changes:
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Pen
  search:
    terms: []
  styles:
    - solid
  unicode: f304
  voted: false
pen-clip:
  aliases:
    names:
      - pen-alt
    unicodes:
      secondary:
        - 10f305
  changes:
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Pen clip
  search:
    terms: []
  styles:
    - solid
  unicode: f305
  voted: false
pen-fancy:
  aliases:
    unicodes:
      composite:
        - 1f58b
        - '2712'
      secondary:
        - 10f5ac
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Pen Fancy
  search:
    terms: []
  styles:
    - solid
  unicode: f5ac
  voted: false
pen-nib:
  aliases:
    unicodes:
      composite:
        - '2711'
      secondary:
        - 10f5ad
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Pen Nib
  search:
    terms: []
  styles:
    - solid
  unicode: f5ad
  voted: true
pen-ruler:
  aliases:
    names:
      - pencil-ruler
    unicodes:
      secondary:
        - 10f5ae
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Pen ruler
  search:
    terms: []
  styles:
    - solid
  unicode: f5ae
  voted: false
pen-to-square:
  aliases:
    names:
      - edit
    unicodes:
      secondary:
        - 10f044
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Pen to square
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f044
  voted: false
pencil:
  aliases:
    names:
      - pencil-alt
    unicodes:
      composite:
        - 270f
        - f040
      primary:
        - f040
      secondary:
        - 10f040
        - 10f303
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: pencil
  search:
    terms: []
  styles:
    - solid
  unicode: f303
  voted: false
people-arrows:
  aliases:
    names:
      - people-arrows-left-right
    unicodes:
      secondary:
        - '10e068'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: People arrows left right
  search:
    terms: []
  styles:
    - solid
  unicode: e068
  voted: false
people-carry-box:
  aliases:
    names:
      - people-carry
    unicodes:
      secondary:
        - 10f4ce
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: People carry box
  search:
    terms: []
  styles:
    - solid
  unicode: f4ce
  voted: false
people-group:
  changes:
    - 6.1.0
    - 6.2.0
  label: People Group
  search:
    terms: []
  styles:
    - solid
  unicode: e533
  voted: false
people-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: People Line
  search:
    terms: []
  styles:
    - solid
  unicode: e534
  voted: false
people-pulling:
  changes:
    - 6.1.0
    - 6.2.0
  label: People Pulling
  search:
    terms: []
  styles:
    - solid
  unicode: e535
  voted: false
people-robbery:
  changes:
    - 6.1.0
    - 6.2.0
  label: People Robbery
  search:
    terms: []
  styles:
    - solid
  unicode: e536
  voted: false
people-roof:
  changes:
    - 6.1.0
    - 6.2.0
  label: People Roof
  search:
    terms: []
  styles:
    - solid
  unicode: e537
  voted: false
pepper-hot:
  aliases:
    unicodes:
      composite:
        - 1f336
      secondary:
        - 10f816
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hot Pepper
  search:
    terms: []
  styles:
    - solid
  unicode: f816
  voted: true
perbyte:
  changes:
    - 5.15.0
  label: PerByte
  search:
    terms: []
  styles:
    - brands
  unicode: e083
  voted: false
percent:
  aliases:
    names:
      - percentage
    unicodes:
      composite:
        - f295
        - f541
      primary:
        - f295
        - f541
      secondary:
        - '1025'
        - 10f295
        - 10f541
  changes:
    - 4.5.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Percent
  search:
    terms: []
  styles:
    - solid
  unicode: '25'
  voted: true
periscope:
  changes:
    - 5.0.0
  label: Periscope
  search:
    terms: []
  styles:
    - brands
  unicode: f3da
  voted: false
person:
  aliases:
    names:
      - male
    unicodes:
      composite:
        - 1f9cd
      secondary:
        - 10f183
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Person
  search:
    terms: []
  styles:
    - solid
  unicode: f183
  voted: false
person-arrow-down-to-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Arrow-down-to-line
  search:
    terms: []
  styles:
    - solid
  unicode: e538
  voted: false
person-arrow-up-from-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Arrow-up-from-line
  search:
    terms: []
  styles:
    - solid
  unicode: e539
  voted: false
person-biking:
  aliases:
    names:
      - biking
    unicodes:
      composite:
        - 1f6b4
      secondary:
        - 10f84a
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Person biking
  search:
    terms: []
  styles:
    - solid
  unicode: f84a
  voted: false
person-booth:
  aliases:
    unicodes:
      secondary:
        - 10f756
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person Entering Booth
  search:
    terms: []
  styles:
    - solid
  unicode: f756
  voted: false
person-breastfeeding:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Breastfeeding
  search:
    terms: []
  styles:
    - solid
  unicode: e53a
  voted: false
person-burst:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Burst
  search:
    terms: []
  styles:
    - solid
  unicode: e53b
  voted: false
person-cane:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Cane
  search:
    terms: []
  styles:
    - solid
  unicode: e53c
  voted: false
person-chalkboard:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Chalkboard
  search:
    terms: []
  styles:
    - solid
  unicode: e53d
  voted: false
person-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e53e
  voted: false
person-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e53f
  voted: false
person-circle-minus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Circle-minus
  search:
    terms: []
  styles:
    - solid
  unicode: e540
  voted: false
person-circle-plus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Circle-plus
  search:
    terms: []
  styles:
    - solid
  unicode: e541
  voted: false
person-circle-question:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Circle-question
  search:
    terms: []
  styles:
    - solid
  unicode: e542
  voted: false
person-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e543
  voted: false
person-digging:
  aliases:
    names:
      - digging
    unicodes:
      secondary:
        - 10f85e
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Person digging
  search:
    terms: []
  styles:
    - solid
  unicode: f85e
  voted: false
person-dots-from-line:
  aliases:
    names:
      - diagnoses
    unicodes:
      secondary:
        - 10f470
  changes:
    - 5.0.7
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person dots from line
  search:
    terms: []
  styles:
    - solid
  unicode: f470
  voted: false
person-dress:
  aliases:
    names:
      - female
    unicodes:
      secondary:
        - 10f182
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Person dress
  search:
    terms: []
  styles:
    - solid
  unicode: f182
  voted: false
person-dress-burst:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Dress BUrst
  search:
    terms: []
  styles:
    - solid
  unicode: e544
  voted: false
person-drowning:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Drowning
  search:
    terms: []
  styles:
    - solid
  unicode: e545
  voted: false
person-falling:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Falling
  search:
    terms: []
  styles:
    - solid
  unicode: e546
  voted: false
person-falling-burst:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Falling Burst
  search:
    terms: []
  styles:
    - solid
  unicode: e547
  voted: false
person-half-dress:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Half-dress
  search:
    terms: []
  styles:
    - solid
  unicode: e548
  voted: false
person-harassing:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Harassing
  search:
    terms: []
  styles:
    - solid
  unicode: e549
  voted: false
person-hiking:
  aliases:
    names:
      - hiking
    unicodes:
      secondary:
        - 10f6ec
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person hiking
  search:
    terms: []
  styles:
    - solid
  unicode: f6ec
  voted: false
person-military-pointing:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Military-pointing
  search:
    terms: []
  styles:
    - solid
  unicode: e54a
  voted: false
person-military-rifle:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Military-rifle
  search:
    terms: []
  styles:
    - solid
  unicode: e54b
  voted: false
person-military-to-person:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Military-to-person
  search:
    terms: []
  styles:
    - solid
  unicode: e54c
  voted: false
person-praying:
  aliases:
    names:
      - pray
    unicodes:
      composite:
        - 1f6d0
      secondary:
        - 10f683
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person praying
  search:
    terms: []
  styles:
    - solid
  unicode: f683
  voted: false
person-pregnant:
  changes:
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Person Pregnant
  search:
    terms: []
  styles:
    - solid
  unicode: e31e
  voted: false
person-rays:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Rays
  search:
    terms: []
  styles:
    - solid
  unicode: e54d
  voted: false
person-rifle:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Rifle
  search:
    terms: []
  styles:
    - solid
  unicode: e54e
  voted: false
person-running:
  aliases:
    names:
      - running
    unicodes:
      composite:
        - 1f3c3
      secondary:
        - 10f70c
  changes:
    - 5.4.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person running
  search:
    terms: []
  styles:
    - solid
  unicode: f70c
  voted: true
person-shelter:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Shelter
  search:
    terms: []
  styles:
    - solid
  unicode: e54f
  voted: false
person-skating:
  aliases:
    names:
      - skating
    unicodes:
      secondary:
        - 10f7c5
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person skating
  search:
    terms: []
  styles:
    - solid
  unicode: f7c5
  voted: false
person-skiing:
  aliases:
    names:
      - skiing
    unicodes:
      composite:
        - 26f7
      secondary:
        - 10f7c9
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person skiing
  search:
    terms: []
  styles:
    - solid
  unicode: f7c9
  voted: false
person-skiing-nordic:
  aliases:
    names:
      - skiing-nordic
    unicodes:
      secondary:
        - 10f7ca
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person skiing nordic
  search:
    terms: []
  styles:
    - solid
  unicode: f7ca
  voted: false
person-snowboarding:
  aliases:
    names:
      - snowboarding
    unicodes:
      composite:
        - 1f3c2
      secondary:
        - 10f7ce
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person snowboarding
  search:
    terms: []
  styles:
    - solid
  unicode: f7ce
  voted: false
person-swimming:
  aliases:
    names:
      - swimmer
    unicodes:
      composite:
        - 1f3ca
      secondary:
        - 10f5c4
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person swimming
  search:
    terms: []
  styles:
    - solid
  unicode: f5c4
  voted: false
person-through-window:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Through-window
  search:
    terms: []
  styles:
    - solid
  unicode: e5a9
  voted: false
person-walking:
  aliases:
    names:
      - walking
    unicodes:
      composite:
        - 1f6b6
      secondary:
        - 10f554
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Person walking
  search:
    terms: []
  styles:
    - solid
  unicode: f554
  voted: true
person-walking-arrow-loop-left:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Walking-arrow-loop-left
  search:
    terms: []
  styles:
    - solid
  unicode: e551
  voted: false
person-walking-arrow-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Walking-arrow-right
  search:
    terms: []
  styles:
    - solid
  unicode: e552
  voted: false
person-walking-dashed-line-arrow-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Walking-dashed-line-arrow-right
  search:
    terms: []
  styles:
    - solid
  unicode: e553
  voted: false
person-walking-luggage:
  changes:
    - 6.1.0
    - 6.2.0
  label: Person Walking-luggage
  search:
    terms: []
  styles:
    - solid
  unicode: e554
  voted: false
person-walking-with-cane:
  aliases:
    names:
      - blind
    unicodes:
      secondary:
        - 10f29d
  changes:
    - 4.6.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Person walking with cane
  search:
    terms: []
  styles:
    - solid
  unicode: f29d
  voted: false
peseta-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Peseta Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e221
  voted: false
peso-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Peso Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e222
  voted: false
phabricator:
  changes:
    - 5.0.0
  label: Phabricator
  search:
    terms: []
  styles:
    - brands
  unicode: f3db
  voted: false
phoenix-framework:
  changes:
    - 5.0.0
    - 5.0.3
  label: Phoenix Framework
  search:
    terms: []
  styles:
    - brands
  unicode: f3dc
  voted: false
phoenix-squadron:
  changes:
    - 5.0.12
    - 5.8.0
  label: Phoenix Squadron
  search:
    terms: []
  styles:
    - brands
  unicode: f511
  voted: false
phone:
  aliases:
    unicodes:
      composite:
        - 1f4de
        - 1f57b
      secondary:
        - 10f095
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Phone
  search:
    terms: []
  styles:
    - solid
  unicode: f095
  voted: false
phone-flip:
  aliases:
    names:
      - phone-alt
    unicodes:
      composite:
        - 1f57d
      secondary:
        - 10f879
  changes:
    - 5.9.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Phone flip
  search:
    terms: []
  styles:
    - solid
  unicode: f879
  voted: false
phone-slash:
  aliases:
    unicodes:
      secondary:
        - 10f3dd
  changes:
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Phone Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f3dd
  voted: false
phone-volume:
  aliases:
    names:
      - volume-control-phone
    unicodes:
      secondary:
        - 10f2a0
  changes:
    - 4.6.0
    - 5.0.0
    - 5.0.3
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Phone Volume
  search:
    terms: []
  styles:
    - solid
  unicode: f2a0
  voted: false
photo-film:
  aliases:
    names:
      - photo-video
    unicodes:
      secondary:
        - 10f87c
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Photo film
  search:
    terms: []
  styles:
    - solid
  unicode: f87c
  voted: false
php:
  changes:
    - 5.0.5
  label: PHP
  search:
    terms: []
  styles:
    - brands
  unicode: f457
  voted: true
pied-piper:
  changes:
    - 4.6.0
    - 5.0.0
    - 5.0.10
    - 5.12.0
  label: Pied Piper Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f2ae
  voted: false
pied-piper-alt:
  changes:
    - 4.1.0
    - 5.0.0
    - 5.7.0
  label: Alternate Pied Piper Logo (Old)
  search:
    terms: []
  styles:
    - brands
  unicode: f1a8
  voted: false
pied-piper-hat:
  changes:
    - 5.0.10
  label: Pied Piper Hat (Old)
  search:
    terms: []
  styles:
    - brands
  unicode: f4e5
  voted: false
pied-piper-pp:
  changes:
    - 4.1.0
    - 5.0.0
  label: Pied Piper PP Logo (Old)
  search:
    terms: []
  styles:
    - brands
  unicode: f1a7
  voted: false
piggy-bank:
  aliases:
    unicodes:
      secondary:
        - 10f4d3
  changes:
    - 5.0.9
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Piggy Bank
  search:
    terms: []
  styles:
    - solid
  unicode: f4d3
  voted: false
pills:
  aliases:
    unicodes:
      secondary:
        - 10f484
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Pills
  search:
    terms: []
  styles:
    - solid
  unicode: f484
  voted: false
pinterest:
  changes:
    - 2.0.0
    - 5.0.0
  label: Pinterest
  search:
    terms: []
  styles:
    - brands
  unicode: f0d2
  voted: false
pinterest-p:
  changes:
    - 4.3.0
    - 5.0.0
  label: Pinterest P
  search:
    terms: []
  styles:
    - brands
  unicode: f231
  voted: false
pix:
  changes:
    - 6.0.0-beta2
  label: Pix
  search:
    terms: []
  styles:
    - brands
  unicode: e43a
  voted: true
pizza-slice:
  aliases:
    unicodes:
      secondary:
        - 10f818
  changes:
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: Pizza Slice
  search:
    terms: []
  styles:
    - solid
  unicode: f818
  voted: true
place-of-worship:
  aliases:
    unicodes:
      secondary:
        - 10f67f
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Place of Worship
  search:
    terms: []
  styles:
    - solid
  unicode: f67f
  voted: false
plane:
  aliases:
    unicodes:
      secondary:
        - 10f072
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: plane
  search:
    terms: []
  styles:
    - solid
  unicode: f072
  voted: false
plane-arrival:
  aliases:
    unicodes:
      composite:
        - 1f6ec
      secondary:
        - 10f5af
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Plane Arrival
  search:
    terms: []
  styles:
    - solid
  unicode: f5af
  voted: false
plane-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plane Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e555
  voted: false
plane-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plane Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e556
  voted: false
plane-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plane Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e557
  voted: false
plane-departure:
  aliases:
    unicodes:
      composite:
        - 1f6eb
      secondary:
        - 10f5b0
  changes:
    - 5.1.0
    - 5.8.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Plane Departure
  search:
    terms: []
  styles:
    - solid
  unicode: f5b0
  voted: false
plane-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plane Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e558
  voted: false
plane-slash:
  aliases:
    unicodes:
      secondary:
        - '10e069'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Plane Slash
  search:
    terms: []
  styles:
    - solid
  unicode: e069
  voted: false
plane-up:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Plane Up
  search:
    terms: []
  styles:
    - solid
  unicode: e22d
  voted: false
plant-wilt:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Plant Wilt
  search:
    terms: []
  styles:
    - solid
  unicode: e5aa
  voted: false
plate-wheat:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Plate Wheat
  search:
    terms: []
  styles:
    - solid
  unicode: e55a
  voted: false
play:
  aliases:
    unicodes:
      composite:
        - 25b6
      secondary:
        - 10f04b
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: play
  search:
    terms: []
  styles:
    - solid
  unicode: f04b
  voted: false
playstation:
  changes:
    - 5.0.0
  label: PlayStation
  search:
    terms: []
  styles:
    - brands
  unicode: f3df
  voted: false
plug:
  aliases:
    unicodes:
      composite:
        - 1f50c
      secondary:
        - 10f1e6
  changes:
    - 4.2.0
    - 5.0.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Plug
  search:
    terms: []
  styles:
    - solid
  unicode: f1e6
  voted: false
plug-circle-bolt:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plug Circle-bolt
  search:
    terms: []
  styles:
    - solid
  unicode: e55b
  voted: false
plug-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plug Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e55c
  voted: false
plug-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plug Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e55d
  voted: false
plug-circle-minus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plug Circle-minus
  search:
    terms: []
  styles:
    - solid
  unicode: e55e
  voted: false
plug-circle-plus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plug Circle-plus
  search:
    terms: []
  styles:
    - solid
  unicode: e55f
  voted: false
plug-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Plug Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e560
  voted: false
plus:
  aliases:
    names:
      - add
    unicodes:
      composite:
        - '2795'
        - f067
      primary:
        - f067
      secondary:
        - 102b
        - 10f067
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: plus
  search:
    terms: []
  styles:
    - solid
  unicode: 2b
  voted: false
plus-minus:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Plus Minus
  search:
    terms: []
  styles:
    - solid
  unicode: e43c
  voted: false
podcast:
  aliases:
    unicodes:
      secondary:
        - 10f2ce
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Podcast
  search:
    terms: []
  styles:
    - solid
  unicode: f2ce
  voted: false
poo:
  aliases:
    unicodes:
      composite:
        - 1f4a9
      secondary:
        - 10f2fe
  changes:
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Poo
  search:
    terms: []
  styles:
    - solid
  unicode: f2fe
  voted: false
poo-storm:
  aliases:
    names:
      - poo-bolt
    unicodes:
      secondary:
        - 10f75a
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Poo bolt
  search:
    terms: []
  styles:
    - solid
  unicode: f75a
  voted: false
poop:
  aliases:
    unicodes:
      secondary:
        - 10f619
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Poop
  search:
    terms: []
  styles:
    - solid
  unicode: f619
  voted: false
power-off:
  aliases:
    unicodes:
      composite:
        - 23fb
      secondary:
        - 10f011
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Power Off
  search:
    terms: []
  styles:
    - solid
  unicode: f011
  voted: false
prescription:
  aliases:
    unicodes:
      secondary:
        - 10f5b1
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Prescription
  search:
    terms: []
  styles:
    - solid
  unicode: f5b1
  voted: false
prescription-bottle:
  aliases:
    unicodes:
      secondary:
        - 10f485
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Prescription Bottle
  search:
    terms: []
  styles:
    - solid
  unicode: f485
  voted: false
prescription-bottle-medical:
  aliases:
    names:
      - prescription-bottle-alt
    unicodes:
      secondary:
        - 10f486
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Prescription bottle medical
  search:
    terms: []
  styles:
    - solid
  unicode: f486
  voted: false
print:
  aliases:
    unicodes:
      composite:
        - 1f5a8
        - 1f5b6
        - '2399'
      secondary:
        - 10f02f
  changes:
    - 1.0.0
    - 5.0.0
    - 5.3.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: print
  search:
    terms: []
  styles:
    - solid
  unicode: f02f
  voted: false
product-hunt:
  changes:
    - 4.5.0
    - 5.0.0
  label: Product Hunt
  search:
    terms: []
  styles:
    - brands
  unicode: f288
  voted: false
pump-medical:
  aliases:
    unicodes:
      secondary:
        - 10e06a
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Pump Medical
  search:
    terms: []
  styles:
    - solid
  unicode: e06a
  voted: false
pump-soap:
  aliases:
    unicodes:
      secondary:
        - 10e06b
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Pump Soap
  search:
    terms: []
  styles:
    - solid
  unicode: e06b
  voted: false
pushed:
  changes:
    - 5.0.0
  label: Pushed
  search:
    terms: []
  styles:
    - brands
  unicode: f3e1
  voted: false
puzzle-piece:
  aliases:
    unicodes:
      composite:
        - 1f9e9
      secondary:
        - 10f12e
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Puzzle Piece
  search:
    terms: []
  styles:
    - solid
  unicode: f12e
  voted: false
python:
  changes:
    - 5.0.0
  label: Python
  search:
    terms: []
  styles:
    - brands
  unicode: f3e2
  voted: false
q:
  aliases:
    unicodes:
      composite:
        - '71'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Q
  search:
    terms: []
  styles:
    - solid
  unicode: '51'
  voted: false
qq:
  changes:
    - 4.1.0
    - 5.0.0
  label: QQ
  search:
    terms: []
  styles:
    - brands
  unicode: f1d6
  voted: false
qrcode:
  aliases:
    unicodes:
      secondary:
        - 10f029
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: qrcode
  search:
    terms: []
  styles:
    - solid
  unicode: f029
  voted: false
question:
  aliases:
    unicodes:
      composite:
        - '2753'
        - '2754'
        - f128
      primary:
        - f128
      secondary:
        - 103f
        - 10f128
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Question
  search:
    terms: []
  styles:
    - solid
  unicode: 3f
  voted: false
quinscape:
  changes:
    - 5.0.5
    - 5.7.0
    - 5.8.0
  label: QuinScape
  search:
    terms: []
  styles:
    - brands
  unicode: f459
  voted: false
quora:
  changes:
    - 4.7.0
    - 5.0.0
  label: Quora
  search:
    terms: []
  styles:
    - brands
  unicode: f2c4
  voted: false
quote-left:
  aliases:
    names:
      - quote-left-alt
    unicodes:
      composite:
        - 201c
      secondary:
        - 10f10d
  changes:
    - 3.0.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: quote-left
  search:
    terms: []
  styles:
    - solid
  unicode: f10d
  voted: false
quote-right:
  aliases:
    names:
      - quote-right-alt
    unicodes:
      composite:
        - 201d
      secondary:
        - 10f10e
  changes:
    - 3.0.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: quote-right
  search:
    terms: []
  styles:
    - solid
  unicode: f10e
  voted: false
r:
  aliases:
    unicodes:
      composite:
        - '72'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: R
  search:
    terms: []
  styles:
    - solid
  unicode: '52'
  voted: false
r-project:
  changes:
    - 5.0.11
    - 5.0.12
  label: R Project
  search:
    terms: []
  styles:
    - brands
  unicode: f4f7
  voted: true
radiation:
  aliases:
    unicodes:
      secondary:
        - 10f7b9
  changes:
    - 5.6.0
    - 5.8.2
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Radiation
  search:
    terms: []
  styles:
    - solid
  unicode: f7b9
  voted: true
radio:
  aliases:
    unicodes:
      composite:
        - 1f4fb
      secondary:
        - 10f8d7
  changes:
    - 5.11.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Radio
  search:
    terms: []
  styles:
    - solid
  unicode: f8d7
  voted: false
rainbow:
  aliases:
    unicodes:
      composite:
        - 1f308
      secondary:
        - 10f75b
  changes:
    - 5.5.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Rainbow
  search:
    terms: []
  styles:
    - solid
  unicode: f75b
  voted: false
ranking-star:
  changes:
    - 6.1.0
    - 6.2.0
  label: Ranking Star
  search:
    terms: []
  styles:
    - solid
  unicode: e561
  voted: false
raspberry-pi:
  changes:
    - 5.6.0
  label: Raspberry Pi
  search:
    terms: []
  styles:
    - brands
  unicode: f7bb
  voted: true
ravelry:
  changes:
    - 4.7.0
    - 5.0.0
    - 5.15.1
  label: Ravelry
  search:
    terms: []
  styles:
    - brands
  unicode: f2d9
  voted: false
react:
  changes:
    - 5.0.0
  label: React
  search:
    terms: []
  styles:
    - brands
  unicode: f41b
  voted: false
reacteurope:
  changes:
    - 5.5.0
    - 5.8.0
  label: ReactEurope
  search:
    terms: []
  styles:
    - brands
  unicode: f75d
  voted: false
readme:
  changes:
    - 5.0.9
    - 5.0.10
  label: ReadMe
  search:
    terms: []
  styles:
    - brands
  unicode: f4d5
  voted: false
rebel:
  changes:
    - 4.1.0
    - 5.0.0
  label: Rebel Alliance
  search:
    terms: []
  styles:
    - brands
  unicode: f1d0
  voted: false
receipt:
  aliases:
    unicodes:
      composite:
        - 1f9fe
      secondary:
        - 10f543
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Receipt
  search:
    terms: []
  styles:
    - solid
  unicode: f543
  voted: true
record-vinyl:
  aliases:
    unicodes:
      secondary:
        - 10f8d9
  changes:
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Record Vinyl
  search:
    terms: []
  styles:
    - solid
  unicode: f8d9
  voted: false
rectangle-ad:
  aliases:
    names:
      - ad
    unicodes:
      secondary:
        - 10f641
  changes:
    - 5.3.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Rectangle ad
  search:
    terms: []
  styles:
    - solid
  unicode: f641
  voted: false
rectangle-list:
  aliases:
    names:
      - list-alt
    unicodes:
      secondary:
        - 10f022
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Rectangle list
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f022
  voted: false
rectangle-xmark:
  aliases:
    names:
      - rectangle-times
      - times-rectangle
      - window-close
    unicodes:
      composite:
        - f2d4
      secondary:
        - 10f410
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Rectangle X Mark
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f410
  voted: false
recycle:
  aliases:
    unicodes:
      composite:
        - '2672'
        - 267a
        - 267b
      secondary:
        - 10f1b8
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Recycle
  search:
    terms: []
  styles:
    - solid
  unicode: f1b8
  voted: false
red-river:
  changes:
    - 5.0.0
  label: red river
  search:
    terms: []
  styles:
    - brands
  unicode: f3e3
  voted: false
reddit:
  changes:
    - 4.1.0
    - 5.0.0
  label: reddit Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a1
  voted: false
reddit-alien:
  changes:
    - 4.5.0
    - 5.0.0
  label: reddit Alien
  search:
    terms: []
  styles:
    - brands
  unicode: f281
  voted: false
redhat:
  changes:
    - 5.6.0
    - 5.8.2
  label: Redhat
  search:
    terms: []
  styles:
    - brands
  unicode: f7bc
  voted: true
registered:
  aliases:
    unicodes:
      composite:
        - ae
      secondary:
        - 10f25d
  changes:
    - 4.4.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Registered Trademark
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f25d
  voted: false
renren:
  changes:
    - 3.2.0
    - 5.0.0
  label: Renren
  search:
    terms: []
  styles:
    - brands
  unicode: f18b
  voted: false
repeat:
  aliases:
    unicodes:
      composite:
        - 1f501
      secondary:
        - 10f363
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Repeat
  search:
    terms: []
  styles:
    - solid
  unicode: f363
  voted: false
reply:
  aliases:
    names:
      - mail-reply
    unicodes:
      composite:
        - f112
      secondary:
        - 10f3e5
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Reply
  search:
    terms: []
  styles:
    - solid
  unicode: f3e5
  voted: false
reply-all:
  aliases:
    names:
      - mail-reply-all
    unicodes:
      secondary:
        - 10f122
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: reply-all
  search:
    terms: []
  styles:
    - solid
  unicode: f122
  voted: false
replyd:
  changes:
    - 5.0.0
  label: replyd
  search:
    terms: []
  styles:
    - brands
  unicode: f3e6
  voted: false
republican:
  aliases:
    unicodes:
      secondary:
        - 10f75e
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Republican
  search:
    terms: []
  styles:
    - solid
  unicode: f75e
  voted: false
researchgate:
  changes:
    - 5.0.11
  label: Researchgate
  search:
    terms: []
  styles:
    - brands
  unicode: f4f8
  voted: true
resolving:
  changes:
    - 5.0.0
  label: Resolving
  search:
    terms: []
  styles:
    - brands
  unicode: f3e7
  voted: false
restroom:
  aliases:
    unicodes:
      secondary:
        - 10f7bd
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Restroom
  search:
    terms: []
  styles:
    - solid
  unicode: f7bd
  voted: true
retweet:
  aliases:
    unicodes:
      secondary:
        - 10f079
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Retweet
  search:
    terms: []
  styles:
    - solid
  unicode: f079
  voted: false
rev:
  changes:
    - 5.1.0
    - 5.1.1
    - 5.8.0
  label: Rev.io
  search:
    terms: []
  styles:
    - brands
  unicode: f5b2
  voted: false
ribbon:
  aliases:
    unicodes:
      composite:
        - 1f397
      secondary:
        - 10f4d6
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Ribbon
  search:
    terms: []
  styles:
    - solid
  unicode: f4d6
  voted: false
right-from-bracket:
  aliases:
    names:
      - sign-out-alt
    unicodes:
      secondary:
        - 10f2f5
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Right from bracket
  search:
    terms: []
  styles:
    - solid
  unicode: f2f5
  voted: false
right-left:
  aliases:
    names:
      - exchange-alt
    unicodes:
      secondary:
        - 10f362
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Right left
  search:
    terms: []
  styles:
    - solid
  unicode: f362
  voted: false
right-long:
  aliases:
    names:
      - long-arrow-alt-right
    unicodes:
      secondary:
        - 10f30b
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Right long
  search:
    terms: []
  styles:
    - solid
  unicode: f30b
  voted: false
right-to-bracket:
  aliases:
    names:
      - sign-in-alt
    unicodes:
      secondary:
        - 10f2f6
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Right to bracket
  search:
    terms: []
  styles:
    - solid
  unicode: f2f6
  voted: false
ring:
  aliases:
    unicodes:
      secondary:
        - 10f70b
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Ring
  search:
    terms: []
  styles:
    - solid
  unicode: f70b
  voted: false
road:
  aliases:
    unicodes:
      composite:
        - 1f6e3
      secondary:
        - 10f018
  changes:
    - 1.0.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: road
  search:
    terms: []
  styles:
    - solid
  unicode: f018
  voted: false
road-barrier:
  changes:
    - 6.1.0
    - 6.2.0
  label: Road Barrier
  search:
    terms: []
  styles:
    - solid
  unicode: e562
  voted: false
road-bridge:
  changes:
    - 6.1.0
    - 6.2.0
  label: Road Bridge
  search:
    terms: []
  styles:
    - solid
  unicode: e563
  voted: false
road-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Road Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e564
  voted: false
road-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Road Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e565
  voted: false
road-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Road Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e566
  voted: false
road-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: Road Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e567
  voted: false
road-spikes:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Road Spikes
  search:
    terms: []
  styles:
    - solid
  unicode: e568
  voted: false
robot:
  aliases:
    unicodes:
      composite:
        - 1f916
      secondary:
        - 10f544
  changes:
    - 5.0.13
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Robot
  search:
    terms: []
  styles:
    - solid
  unicode: f544
  voted: true
rocket:
  aliases:
    unicodes:
      secondary:
        - 10f135
  changes:
    - 3.1.0
    - 5.0.0
    - 5.7.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: rocket
  search:
    terms: []
  styles:
    - solid
  unicode: f135
  voted: false
rocketchat:
  changes:
    - 5.0.0
    - 5.4.2
    - 5.8.0
    - 5.15.0
  label: Rocket.Chat
  search:
    terms: []
  styles:
    - brands
  unicode: f3e8
  voted: false
rockrms:
  changes:
    - 5.0.0
  label: Rockrms
  search:
    terms: []
  styles:
    - brands
  unicode: f3e9
  voted: false
rotate:
  aliases:
    names:
      - sync-alt
    unicodes:
      composite:
        - 1f504
      secondary:
        - 10f2f1
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Rotate
  search:
    terms: []
  styles:
    - solid
  unicode: f2f1
  voted: false
rotate-left:
  aliases:
    names:
      - rotate-back
      - rotate-backward
      - undo-alt
    unicodes:
      secondary:
        - 10f2ea
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Rotate Left
  search:
    terms: []
  styles:
    - solid
  unicode: f2ea
  voted: false
rotate-right:
  aliases:
    names:
      - redo-alt
      - rotate-forward
    unicodes:
      secondary:
        - 10f2f9
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Rotate Right
  search:
    terms: []
  styles:
    - solid
  unicode: f2f9
  voted: false
route:
  aliases:
    unicodes:
      secondary:
        - 10f4d7
  changes:
    - 5.0.9
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Route
  search:
    terms: []
  styles:
    - solid
  unicode: f4d7
  voted: false
rss:
  aliases:
    names:
      - feed
    unicodes:
      secondary:
        - 10f09e
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: rss
  search:
    terms: []
  styles:
    - solid
  unicode: f09e
  voted: false
ruble-sign:
  aliases:
    names:
      - rouble
      - rub
      - ruble
    unicodes:
      composite:
        - 20bd
      secondary:
        - 10f158
  changes:
    - 4.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Ruble Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f158
  voted: false
rug:
  changes:
    - 6.1.0
    - 6.2.0
  label: Rug
  search:
    terms: []
  styles:
    - solid
  unicode: e569
  voted: false
ruler:
  aliases:
    unicodes:
      composite:
        - 1f4cf
      secondary:
        - 10f545
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Ruler
  search:
    terms: []
  styles:
    - solid
  unicode: f545
  voted: true
ruler-combined:
  aliases:
    unicodes:
      secondary:
        - 10f546
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Ruler Combined
  search:
    terms: []
  styles:
    - solid
  unicode: f546
  voted: true
ruler-horizontal:
  aliases:
    unicodes:
      secondary:
        - 10f547
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Ruler Horizontal
  search:
    terms: []
  styles:
    - solid
  unicode: f547
  voted: true
ruler-vertical:
  aliases:
    unicodes:
      secondary:
        - 10f548
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Ruler Vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f548
  voted: true
rupee-sign:
  aliases:
    names:
      - rupee
    unicodes:
      composite:
        - 20a8
      secondary:
        - 10f156
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Indian Rupee Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f156
  voted: false
rupiah-sign:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Rupiah Sign
  search:
    terms: []
  styles:
    - solid
  unicode: e23d
  voted: false
rust:
  changes:
    - 5.13.1
    - 5.14.0
  label: Rust
  search:
    terms: []
  styles:
    - brands
  unicode: e07a
  voted: true
s:
  aliases:
    unicodes:
      composite:
        - '73'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: S
  search:
    terms: []
  styles:
    - solid
  unicode: '53'
  voted: false
sack-dollar:
  aliases:
    unicodes:
      composite:
        - 1f4b0
      secondary:
        - 10f81d
  changes:
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Sack of Money
  search:
    terms: []
  styles:
    - solid
  unicode: f81d
  voted: true
sack-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: Sack Xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e56a
  voted: false
safari:
  changes:
    - 4.4.0
    - 5.0.0
    - 5.12.0
  label: Safari
  search:
    terms: []
  styles:
    - brands
  unicode: f267
  voted: false
sailboat:
  changes:
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Sailboat
  search:
    terms: []
  styles:
    - solid
  unicode: e445
  voted: false
salesforce:
  changes:
    - 5.8.0
  label: Salesforce
  search:
    terms: []
  styles:
    - brands
  unicode: f83b
  voted: false
sass:
  changes:
    - 5.0.0
    - 5.8.0
  label: Sass
  search:
    terms: []
  styles:
    - brands
  unicode: f41e
  voted: false
satellite:
  aliases:
    unicodes:
      composite:
        - 1f6f0
      secondary:
        - 10f7bf
  changes:
    - 5.6.0
    - 5.10.1
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Satellite
  search:
    terms: []
  styles:
    - solid
  unicode: f7bf
  voted: true
satellite-dish:
  aliases:
    unicodes:
      composite:
        - 1f4e1
      secondary:
        - 10f7c0
  changes:
    - 5.6.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Satellite Dish
  search:
    terms: []
  styles:
    - solid
  unicode: f7c0
  voted: true
scale-balanced:
  aliases:
    names:
      - balance-scale
    unicodes:
      composite:
        - '2696'
      secondary:
        - 10f24e
  changes:
    - 4.4.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Scale balanced
  search:
    terms: []
  styles:
    - solid
  unicode: f24e
  voted: false
scale-unbalanced:
  aliases:
    names:
      - balance-scale-left
    unicodes:
      secondary:
        - 10f515
  changes:
    - 5.0.13
    - 5.9.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Scale unbalanced
  search:
    terms: []
  styles:
    - solid
  unicode: f515
  voted: true
scale-unbalanced-flip:
  aliases:
    names:
      - balance-scale-right
    unicodes:
      secondary:
        - 10f516
  changes:
    - 5.0.13
    - 5.9.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Scale unbalanced flip
  search:
    terms: []
  styles:
    - solid
  unicode: f516
  voted: true
schlix:
  changes:
    - 5.0.0
  label: SCHLIX
  search:
    terms: []
  styles:
    - brands
  unicode: f3ea
  voted: false
school:
  aliases:
    unicodes:
      composite:
        - 1f3eb
      secondary:
        - 10f549
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: School
  search:
    terms: []
  styles:
    - solid
  unicode: f549
  voted: true
school-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: School Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e56b
  voted: false
school-circle-exclamation:
  changes:
    - 6.1.0
    - 6.2.0
  label: School Circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e56c
  voted: false
school-circle-xmark:
  changes:
    - 6.1.0
    - 6.2.0
  label: School Circle-xmark
  search:
    terms: []
  styles:
    - solid
  unicode: e56d
  voted: false
school-flag:
  changes:
    - 6.1.0
    - 6.2.0
  label: School Flag
  search:
    terms: []
  styles:
    - solid
  unicode: e56e
  voted: false
school-lock:
  changes:
    - 6.1.0
    - 6.2.0
  label: School Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e56f
  voted: false
scissors:
  aliases:
    names:
      - cut
    unicodes:
      composite:
        - '2700'
        - '2702'
        - '2704'
      secondary:
        - 10f0c4
  changes:
    - 2.0.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Scissors
  search:
    terms: []
  styles:
    - solid
  unicode: f0c4
  voted: false
screenpal:
  changes:
    - 6.1.0
  label: Screenpal
  search:
    terms: []
  styles:
    - brands
  unicode: e570
  voted: false
screwdriver:
  aliases:
    unicodes:
      composite:
        - 1fa9b
      secondary:
        - 10f54a
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Screwdriver
  search:
    terms: []
  styles:
    - solid
  unicode: f54a
  voted: true
screwdriver-wrench:
  aliases:
    names:
      - tools
    unicodes:
      secondary:
        - 10f7d9
  changes:
    - 5.6.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Screwdriver wrench
  search:
    terms: []
  styles:
    - solid
  unicode: f7d9
  voted: true
scribd:
  changes:
    - 4.5.0
    - 5.0.0
  label: Scribd
  search:
    terms: []
  styles:
    - brands
  unicode: f28a
  voted: false
scroll:
  aliases:
    unicodes:
      composite:
        - 1f4dc
      secondary:
        - 10f70e
  changes:
    - 5.4.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Scroll
  search:
    terms: []
  styles:
    - solid
  unicode: f70e
  voted: false
scroll-torah:
  aliases:
    names:
      - torah
    unicodes:
      secondary:
        - 10f6a0
  changes:
    - 5.3.0
    - 5.7.0
    - 5.9.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Scroll torah
  search:
    terms: []
  styles:
    - solid
  unicode: f6a0
  voted: false
sd-card:
  aliases:
    unicodes:
      secondary:
        - 10f7c2
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Sd Card
  search:
    terms: []
  styles:
    - solid
  unicode: f7c2
  voted: true
searchengin:
  changes:
    - 5.0.0
  label: Searchengin
  search:
    terms: []
  styles:
    - brands
  unicode: f3eb
  voted: false
section:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Section
  search:
    terms: []
  styles:
    - solid
  unicode: e447
  voted: true
seedling:
  aliases:
    names:
      - sprout
    unicodes:
      composite:
        - 1f331
      secondary:
        - 10f4d8
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Seedling
  search:
    terms: []
  styles:
    - solid
  unicode: f4d8
  voted: false
sellcast:
  changes:
    - 5.0.0
  label: Sellcast
  search:
    terms: []
  styles:
    - brands
  unicode: f2da
  voted: false
sellsy:
  changes:
    - 4.3.0
    - 5.0.0
  label: Sellsy
  search:
    terms: []
  styles:
    - brands
  unicode: f213
  voted: false
server:
  aliases:
    unicodes:
      secondary:
        - 10f233
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Server
  search:
    terms: []
  styles:
    - solid
  unicode: f233
  voted: false
servicestack:
  changes:
    - 5.0.0
  label: Servicestack
  search:
    terms: []
  styles:
    - brands
  unicode: f3ec
  voted: false
shapes:
  aliases:
    names:
      - triangle-circle-square
    unicodes:
      secondary:
        - 10f61f
  changes:
    - 5.2.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Shapes
  search:
    terms: []
  styles:
    - solid
  unicode: f61f
  voted: false
share:
  aliases:
    names:
      - arrow-turn-right
      - mail-forward
    unicodes:
      secondary:
        - 10f064
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Share
  search:
    terms: []
  styles:
    - solid
  unicode: f064
  voted: false
share-from-square:
  aliases:
    names:
      - share-square
    unicodes:
      composite:
        - f045
      secondary:
        - 10f14d
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Share from square
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f14d
  voted: false
share-nodes:
  aliases:
    names:
      - share-alt
    unicodes:
      secondary:
        - 10f1e0
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.1.2
    - 6.2.0
  label: Share nodes
  search:
    terms: []
  styles:
    - solid
  unicode: f1e0
  voted: false
sheet-plastic:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Sheet Plastic
  search:
    terms: []
  styles:
    - solid
  unicode: e571
  voted: false
shekel-sign:
  aliases:
    names:
      - ils
      - shekel
      - sheqel
      - sheqel-sign
    unicodes:
      composite:
        - 20aa
      secondary:
        - 10f20b
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Shekel Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f20b
  voted: true
shield:
  aliases:
    names:
      - shield-blank
    unicodes:
      composite:
        - 1f6e1
      secondary:
        - 10f132
  changes:
    - 3.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: shield
  search:
    terms: []
  styles:
    - solid
  unicode: f132
  voted: false
shield-cat:
  changes:
    - 6.1.0
    - 6.2.0
  label: Shield Cat
  search:
    terms: []
  styles:
    - solid
  unicode: e572
  voted: false
shield-dog:
  changes:
    - 6.1.0
    - 6.2.0
  label: Shield Dog
  search:
    terms: []
  styles:
    - solid
  unicode: e573
  voted: false
shield-halved:
  aliases:
    names:
      - shield-alt
    unicodes:
      secondary:
        - 10f3ed
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Shield Halved
  search:
    terms: []
  styles:
    - solid
  unicode: f3ed
  voted: false
shield-heart:
  changes:
    - 6.1.0
    - 6.2.0
  label: Shield Heart
  search:
    terms: []
  styles:
    - solid
  unicode: e574
  voted: false
shield-virus:
  aliases:
    unicodes:
      secondary:
        - 10e06c
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Shield Virus
  search:
    terms: []
  styles:
    - solid
  unicode: e06c
  voted: false
ship:
  aliases:
    unicodes:
      composite:
        - 1f6a2
      secondary:
        - 10f21a
  changes:
    - 4.3.0
    - 5.0.0
    - 5.10.2
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Ship
  search:
    terms: []
  styles:
    - solid
  unicode: f21a
  voted: false
shirt:
  aliases:
    names:
      - t-shirt
      - tshirt
    unicodes:
      composite:
        - 1f455
      secondary:
        - 10f553
  changes:
    - 5.0.13
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: T-Shirt
  search:
    terms: []
  styles:
    - solid
  unicode: f553
  voted: true
shirtsinbulk:
  changes:
    - 4.3.0
    - 5.0.0
    - 5.7.0
  label: Shirts in Bulk
  search:
    terms: []
  styles:
    - brands
  unicode: f214
  voted: false
shoe-prints:
  aliases:
    unicodes:
      secondary:
        - 10f54b
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Shoe Prints
  search:
    terms: []
  styles:
    - solid
  unicode: f54b
  voted: true
shop:
  aliases:
    names:
      - store-alt
    unicodes:
      secondary:
        - 10f54f
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Shop
  search:
    terms: []
  styles:
    - solid
  unicode: f54f
  voted: true
shop-lock:
  changes:
    - 6.0.0
    - 6.1.0
    - 6.2.0
  label: Shop Lock
  search:
    terms: []
  styles:
    - solid
  unicode: e4a5
  voted: false
shop-slash:
  aliases:
    names:
      - store-alt-slash
    unicodes:
      secondary:
        - '10e070'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Shop slash
  search:
    terms: []
  styles:
    - solid
  unicode: e070
  voted: false
shopify:
  changes:
    - 5.12.1
    - 5.14.0
  label: Shopify
  search:
    terms: []
  styles:
    - brands
  unicode: e057
  voted: false
shopware:
  changes:
    - 5.1.0
    - 5.8.0
  label: Shopware
  search:
    terms: []
  styles:
    - brands
  unicode: f5b5
  voted: false
shower:
  aliases:
    unicodes:
      composite:
        - 1f6bf
      secondary:
        - 10f2cc
  changes:
    - 4.7.0
    - 5.0.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Shower
  search:
    terms: []
  styles:
    - solid
  unicode: f2cc
  voted: false
shrimp:
  aliases:
    unicodes:
      composite:
        - 1f990
  changes:
    - 6.0.0-beta2
    - 6.2.0
  label: Shrimp
  search:
    terms: []
  styles:
    - solid
  unicode: e448
  voted: false
shuffle:
  aliases:
    names:
      - random
    unicodes:
      composite:
        - 1f500
      secondary:
        - 10f074
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Shuffle
  search:
    terms: []
  styles:
    - solid
  unicode: f074
  voted: false
shuttle-space:
  aliases:
    names:
      - space-shuttle
    unicodes:
      secondary:
        - 10f197
  changes:
    - 4.1.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Shuttle space
  search:
    terms: []
  styles:
    - solid
  unicode: f197
  voted: false
sign-hanging:
  aliases:
    names:
      - sign
    unicodes:
      secondary:
        - 10f4d9
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Sign hanging
  search:
    terms: []
  styles:
    - solid
  unicode: f4d9
  voted: false
signal:
  aliases:
    names:
      - signal-5
      - signal-perfect
    unicodes:
      composite:
        - 1f4f6
      secondary:
        - 10f012
  changes:
    - 1.0.0
    - 5.0.0
    - 5.3.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: signal
  search:
    terms: []
  styles:
    - solid
  unicode: f012
  voted: false
signature:
  aliases:
    unicodes:
      secondary:
        - 10f5b7
  changes:
    - 5.1.0
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Signature
  search:
    terms: []
  styles:
    - solid
  unicode: f5b7
  voted: true
signs-post:
  aliases:
    names:
      - map-signs
    unicodes:
      secondary:
        - 10f277
  changes:
    - 4.4.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Signs post
  search:
    terms: []
  styles:
    - solid
  unicode: f277
  voted: false
sim-card:
  aliases:
    unicodes:
      secondary:
        - 10f7c4
  changes:
    - 5.6.0
    - 5.8.2
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: SIM Card
  search:
    terms: []
  styles:
    - solid
  unicode: f7c4
  voted: true
simplybuilt:
  changes:
    - 4.3.0
    - 5.0.0
  label: SimplyBuilt
  search:
    terms: []
  styles:
    - brands
  unicode: f215
  voted: false
sink:
  aliases:
    unicodes:
      secondary:
        - 10e06d
  changes:
    - 5.13.0
    - 5.13.1
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Sink
  search:
    terms: []
  styles:
    - solid
  unicode: e06d
  voted: false
sistrix:
  changes:
    - 5.0.0
  label: SISTRIX
  search:
    terms: []
  styles:
    - brands
  unicode: f3ee
  voted: false
sitemap:
  aliases:
    unicodes:
      secondary:
        - 10f0e8
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Sitemap
  search:
    terms: []
  styles:
    - solid
  unicode: f0e8
  voted: false
sith:
  changes:
    - 5.0.12
  label: Sith
  search:
    terms: []
  styles:
    - brands
  unicode: f512
  voted: false
sitrox:
  changes:
    - 6.0.0-beta2
  label: Sitrox
  search:
    terms: []
  styles:
    - brands
  unicode: e44a
  voted: false
sketch:
  changes:
    - 5.6.0
    - 5.8.0
  label: Sketch
  search:
    terms: []
  styles:
    - brands
  unicode: f7c6
  voted: false
skull:
  aliases:
    unicodes:
      composite:
        - 1f480
      secondary:
        - 10f54c
  changes:
    - 5.0.13
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Skull
  search:
    terms: []
  styles:
    - solid
  unicode: f54c
  voted: true
skull-crossbones:
  aliases:
    unicodes:
      composite:
        - 1f571
        - '2620'
      secondary:
        - 10f714
  changes:
    - 5.4.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Skull & Crossbones
  search:
    terms: []
  styles:
    - solid
  unicode: f714
  voted: false
skyatlas:
  changes:
    - 4.3.0
    - 5.0.0
    - 5.0.3
  label: skyatlas
  search:
    terms: []
  styles:
    - brands
  unicode: f216
  voted: false
skype:
  changes:
    - 3.2.0
    - 5.0.0
  label: Skype
  search:
    terms: []
  styles:
    - brands
  unicode: f17e
  voted: false
slack:
  aliases:
    names:
      - slack-hash
    unicodes:
      composite:
        - f3ef
  changes:
    - 4.1.0
    - 5.0.0
    - 5.7.0
  label: Slack Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f198
  voted: false
slash:
  aliases:
    unicodes:
      secondary:
        - 10f715
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f715
  voted: true
sleigh:
  aliases:
    unicodes:
      secondary:
        - 10f7cc
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Sleigh
  search:
    terms: []
  styles:
    - solid
  unicode: f7cc
  voted: false
sliders:
  aliases:
    names:
      - sliders-h
    unicodes:
      secondary:
        - 10f1de
  changes:
    - 4.1.0
    - 5.0.0
    - 5.0.11
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Sliders
  search:
    terms: []
  styles:
    - solid
  unicode: f1de
  voted: false
slideshare:
  changes:
    - 4.2.0
    - 5.0.0
  label: Slideshare
  search:
    terms: []
  styles:
    - brands
  unicode: f1e7
  voted: false
smog:
  aliases:
    unicodes:
      secondary:
        - 10f75f
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Smog
  search:
    terms: []
  styles:
    - solid
  unicode: f75f
  voted: false
smoking:
  aliases:
    unicodes:
      composite:
        - 1f6ac
      secondary:
        - 10f48d
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Smoking
  search:
    terms: []
  styles:
    - solid
  unicode: f48d
  voted: true
snapchat:
  aliases:
    names:
      - snapchat-ghost
    unicodes:
      composite:
        - f2ac
  changes:
    - 4.6.0
    - 5.0.0
    - 6.0.0-beta1
  label: Snapchat
  search:
    terms: []
  styles:
    - brands
  unicode: f2ab
  voted: false
snowflake:
  aliases:
    unicodes:
      composite:
        - '2744'
        - '2746'
      secondary:
        - 10f2dc
  changes:
    - 4.7.0
    - 5.0.0
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Snowflake
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2dc
  voted: false
snowman:
  aliases:
    unicodes:
      composite:
        - '2603'
        - 26c4
      secondary:
        - 10f7d0
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Snowman
  search:
    terms: []
  styles:
    - solid
  unicode: f7d0
  voted: false
snowplow:
  aliases:
    unicodes:
      secondary:
        - 10f7d2
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Snowplow
  search:
    terms: []
  styles:
    - solid
  unicode: f7d2
  voted: false
soap:
  aliases:
    unicodes:
      composite:
        - 1f9fc
      secondary:
        - 10e06e
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Soap
  search:
    terms: []
  styles:
    - solid
  unicode: e06e
  voted: false
socks:
  aliases:
    unicodes:
      composite:
        - 1f9e6
      secondary:
        - 10f696
  changes:
    - 5.3.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Socks
  search:
    terms: []
  styles:
    - solid
  unicode: f696
  voted: false
solar-panel:
  aliases:
    unicodes:
      secondary:
        - 10f5ba
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Solar Panel
  search:
    terms: []
  styles:
    - solid
  unicode: f5ba
  voted: false
sort:
  aliases:
    names:
      - unsorted
    unicodes:
      secondary:
        - 10f0dc
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Sort
  search:
    terms: []
  styles:
    - solid
  unicode: f0dc
  voted: false
sort-down:
  aliases:
    names:
      - sort-desc
    unicodes:
      secondary:
        - 10f0dd
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Sort Down (Descending)
  search:
    terms: []
  styles:
    - solid
  unicode: f0dd
  voted: false
sort-up:
  aliases:
    names:
      - sort-asc
    unicodes:
      secondary:
        - 10f0de
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Sort Up (Ascending)
  search:
    terms: []
  styles:
    - solid
  unicode: f0de
  voted: false
soundcloud:
  changes:
    - 4.1.0
    - 5.0.0
  label: SoundCloud
  search:
    terms: []
  styles:
    - brands
  unicode: f1be
  voted: false
sourcetree:
  changes:
    - 5.6.0
    - 5.8.0
  label: Sourcetree
  search:
    terms: []
  styles:
    - brands
  unicode: f7d3
  voted: true
spa:
  aliases:
    unicodes:
      secondary:
        - 10f5bb
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Spa
  search:
    terms: []
  styles:
    - solid
  unicode: f5bb
  voted: false
space-awesome:
  changes:
    - 6.1.2
  label: Space Awesome
  search:
    terms: []
  styles:
    - brands
  unicode: e5ac
  voted: false
spaghetti-monster-flying:
  aliases:
    names:
      - pastafarianism
    unicodes:
      secondary:
        - 10f67b
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Spaghetti monster flying
  search:
    terms: []
  styles:
    - solid
  unicode: f67b
  voted: false
speakap:
  changes:
    - 5.0.0
    - 5.4.0
    - 5.8.0
  label: Speakap
  search:
    terms: []
  styles:
    - brands
  unicode: f3f3
  voted: false
speaker-deck:
  changes:
    - 5.8.0
  label: Speaker Deck
  search:
    terms: []
  styles:
    - brands
  unicode: f83c
  voted: false
spell-check:
  aliases:
    unicodes:
      secondary:
        - 10f891
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Spell Check
  search:
    terms: []
  styles:
    - solid
  unicode: f891
  voted: false
spider:
  aliases:
    unicodes:
      composite:
        - 1f577
      secondary:
        - 10f717
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Spider
  search:
    terms: []
  styles:
    - solid
  unicode: f717
  voted: true
spinner:
  aliases:
    unicodes:
      secondary:
        - 10f110
  changes:
    - 3.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Spinner
  search:
    terms: []
  styles:
    - solid
  unicode: f110
  voted: false
splotch:
  aliases:
    unicodes:
      secondary:
        - 10f5bc
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Splotch
  search:
    terms: []
  styles:
    - solid
  unicode: f5bc
  voted: false
spoon:
  aliases:
    names:
      - utensil-spoon
    unicodes:
      composite:
        - 1f944
        - f1b1
      secondary:
        - 10f2e5
  changes:
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Spoon
  search:
    terms: []
  styles:
    - solid
  unicode: f2e5
  voted: false
spotify:
  changes:
    - 4.1.0
    - 5.0.0
  label: Spotify
  search:
    terms: []
  styles:
    - brands
  unicode: f1bc
  voted: false
spray-can:
  aliases:
    unicodes:
      secondary:
        - 10f5bd
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Spray Can
  search:
    terms: []
  styles:
    - solid
  unicode: f5bd
  voted: false
spray-can-sparkles:
  aliases:
    names:
      - air-freshener
    unicodes:
      secondary:
        - 10f5d0
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Spray Can Sparkles
  search:
    terms: []
  styles:
    - solid
  unicode: f5d0
  voted: false
square:
  aliases:
    unicodes:
      composite:
        - 25a0
        - 25fb
        - 25fc
        - f096
      secondary:
        - 10f0c8
  changes:
    - 2.0.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0c8
  voted: false
square-arrow-up-right:
  aliases:
    names:
      - external-link-square
    unicodes:
      secondary:
        - 10f14c
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square arrow up right
  search:
    terms: []
  styles:
    - solid
  unicode: f14c
  voted: false
square-behance:
  aliases:
    names:
      - behance-square
  changes:
    - 4.1.0
    - 5.0.0
    - 5.0.3
    - 6.1.2
  label: Behance Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1b5
  voted: false
square-caret-down:
  aliases:
    names:
      - caret-square-down
    unicodes:
      secondary:
        - 10f150
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square caret down
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f150
  voted: false
square-caret-left:
  aliases:
    names:
      - caret-square-left
    unicodes:
      secondary:
        - 10f191
  changes:
    - 4.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square caret left
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f191
  voted: false
square-caret-right:
  aliases:
    names:
      - caret-square-right
    unicodes:
      secondary:
        - 10f152
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square caret right
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f152
  voted: false
square-caret-up:
  aliases:
    names:
      - caret-square-up
    unicodes:
      secondary:
        - 10f151
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square caret up
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f151
  voted: false
square-check:
  aliases:
    names:
      - check-square
    unicodes:
      composite:
        - '2611'
        - '2705'
        - f046
      secondary:
        - 10f14a
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square check
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f14a
  voted: false
square-dribbble:
  aliases:
    names:
      - dribbble-square
  changes:
    - 5.0.0
    - 6.1.2
  label: Dribbble Square
  search:
    terms: []
  styles:
    - brands
  unicode: f397
  voted: false
square-envelope:
  aliases:
    names:
      - envelope-square
    unicodes:
      secondary:
        - 10f199
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square envelope
  search:
    terms: []
  styles:
    - solid
  unicode: f199
  voted: false
square-facebook:
  aliases:
    names:
      - facebook-square
  changes:
    - 1.0.0
    - 5.0.0
    - 5.8.2
    - 6.1.2
  label: Facebook Square
  search:
    terms: []
  styles:
    - brands
  unicode: f082
  voted: false
square-font-awesome:
  changes:
    - 5.0.0
    - 5.0.1
    - 6.0.0-beta1
    - 6.1.2
  label: Font Awesome in Square
  search:
    terms: []
  styles:
    - brands
  unicode: e5ad
  voted: false
square-font-awesome-stroke:
  aliases:
    names:
      - font-awesome-alt
  changes:
    - 5.0.0
    - 6.0.0-beta1
  label: Font Awesome in Square with Stroke Outline
  search:
    terms: []
  styles:
    - brands
  unicode: f35c
  voted: false
square-full:
  aliases:
    unicodes:
      composite:
        - 1f7e5
        - 1f7e6
        - 1f7e7
        - 1f7e8
        - 1f7e9
        - 1f7ea
        - 1f7eb
        - 2b1b
        - 2b1c
      secondary:
        - 10f45c
  changes:
    - 5.0.5
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square Full
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f45c
  voted: false
square-git:
  aliases:
    names:
      - git-square
  changes:
    - 4.1.0
    - 5.0.0
    - 5.8.2
    - 6.1.2
  label: Git Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1d2
  voted: false
square-github:
  aliases:
    names:
      - github-square
  changes:
    - 1.0.0
    - 5.0.0
    - 6.1.2
  label: GitHub Square
  search:
    terms: []
  styles:
    - brands
  unicode: f092
  voted: false
square-gitlab:
  aliases:
    names:
      - gitlab-square
  changes:
    - 6.1.2
  label: Square Gitlab
  search:
    terms: []
  styles:
    - brands
  unicode: e5ae
  voted: false
square-google-plus:
  aliases:
    names:
      - google-plus-square
  changes:
    - 2.0.0
    - 5.0.0
    - 6.1.2
  label: Google Plus Square
  search:
    terms: []
  styles:
    - brands
  unicode: f0d4
  voted: false
square-h:
  aliases:
    names:
      - h-square
    unicodes:
      secondary:
        - 10f0fd
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square h
  search:
    terms: []
  styles:
    - solid
  unicode: f0fd
  voted: false
square-hacker-news:
  aliases:
    names:
      - hacker-news-square
  changes:
    - 5.0.0
    - 6.1.2
  label: Hacker News Square
  search:
    terms: []
  styles:
    - brands
  unicode: f3af
  voted: false
square-instagram:
  aliases:
    names:
      - instagram-square
  changes:
    - 5.12.1
    - 5.14.0
    - 6.1.2
  label: Instagram Square
  search:
    terms: []
  styles:
    - brands
  unicode: e055
  voted: true
square-js:
  aliases:
    names:
      - js-square
  changes:
    - 5.0.0
    - 5.0.3
    - 6.1.2
  label: JavaScript (JS) Square
  search:
    terms: []
  styles:
    - brands
  unicode: f3b9
  voted: false
square-lastfm:
  aliases:
    names:
      - lastfm-square
  changes:
    - 4.2.0
    - 5.0.0
    - 5.0.11
    - 6.1.2
  label: last.fm Square
  search:
    terms: []
  styles:
    - brands
  unicode: f203
  voted: false
square-minus:
  aliases:
    names:
      - minus-square
    unicodes:
      composite:
        - f147
      secondary:
        - 10f146
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square minus
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f146
  voted: false
square-nfi:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Square Nfi
  search:
    terms: []
  styles:
    - solid
  unicode: e576
  voted: false
square-odnoklassniki:
  aliases:
    names:
      - odnoklassniki-square
  changes:
    - 4.4.0
    - 5.0.0
    - 6.1.2
  label: Odnoklassniki Square
  search:
    terms: []
  styles:
    - brands
  unicode: f264
  voted: false
square-parking:
  aliases:
    names:
      - parking
    unicodes:
      composite:
        - 1f17f
      secondary:
        - 10f540
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Square parking
  search:
    terms: []
  styles:
    - solid
  unicode: f540
  voted: true
square-pen:
  aliases:
    names:
      - pen-square
      - pencil-square
    unicodes:
      secondary:
        - 10f14b
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square pen
  search:
    terms: []
  styles:
    - solid
  unicode: f14b
  voted: false
square-person-confined:
  changes:
    - 6.1.0
    - 6.2.0
  label: Square Person-confined
  search:
    terms: []
  styles:
    - solid
  unicode: e577
  voted: false
square-phone:
  aliases:
    names:
      - phone-square
    unicodes:
      secondary:
        - 10f098
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square phone
  search:
    terms: []
  styles:
    - solid
  unicode: f098
  voted: false
square-phone-flip:
  aliases:
    names:
      - phone-square-alt
    unicodes:
      secondary:
        - 10f87b
  changes:
    - 5.9.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.2.0
  label: Square phone flip
  search:
    terms: []
  styles:
    - solid
  unicode: f87b
  voted: false
square-pied-piper:
  aliases:
    names:
      - pied-piper-square
  changes:
    - 5.12.0
    - 5.14.0
    - 6.1.2
  label: Pied Piper Square Logo (Old)
  search:
    terms: []
  styles:
    - brands
  unicode: e01e
  voted: false
square-pinterest:
  aliases:
    names:
      - pinterest-square
  changes:
    - 2.0.0
    - 5.0.0
    - 6.1.2
  label: Pinterest Square
  search:
    terms: []
  styles:
    - brands
  unicode: f0d3
  voted: false
square-plus:
  aliases:
    names:
      - plus-square
    unicodes:
      composite:
        - f196
      secondary:
        - 10f0fe
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square plus
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f0fe
  voted: false
square-poll-horizontal:
  aliases:
    names:
      - poll-h
    unicodes:
      secondary:
        - 10f682
  changes:
    - 5.3.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Square poll horizontal
  search:
    terms: []
  styles:
    - solid
  unicode: f682
  voted: false
square-poll-vertical:
  aliases:
    names:
      - poll
    unicodes:
      secondary:
        - 10f681
  changes:
    - 5.3.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Square poll vertical
  search:
    terms: []
  styles:
    - solid
  unicode: f681
  voted: false
square-reddit:
  aliases:
    names:
      - reddit-square
  changes:
    - 4.1.0
    - 5.0.0
    - 6.1.2
  label: reddit Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1a2
  voted: false
square-root-variable:
  aliases:
    names:
      - square-root-alt
    unicodes:
      secondary:
        - 10f698
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square root variable
  search:
    terms: []
  styles:
    - solid
  unicode: f698
  voted: false
square-rss:
  aliases:
    names:
      - rss-square
    unicodes:
      secondary:
        - 10f143
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square rss
  search:
    terms: []
  styles:
    - solid
  unicode: f143
  voted: false
square-share-nodes:
  aliases:
    names:
      - share-alt-square
    unicodes:
      secondary:
        - 10f1e1
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square share nodes
  search:
    terms: []
  styles:
    - solid
  unicode: f1e1
  voted: false
square-snapchat:
  aliases:
    names:
      - snapchat-square
  changes:
    - 4.6.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.1.2
  label: Snapchat Square
  search:
    terms: []
  styles:
    - brands
  unicode: f2ad
  voted: false
square-steam:
  aliases:
    names:
      - steam-square
  changes:
    - 4.1.0
    - 5.0.0
    - 6.1.2
  label: Steam Square
  search:
    terms: []
  styles:
    - brands
  unicode: f1b7
  voted: false
square-tumblr:
  aliases:
    names:
      - tumblr-square
  changes:
    - 3.2.0
    - 5.0.0
    - 6.1.2
  label: Tumblr Square
  search:
    terms: []
  styles:
    - brands
  unicode: f174
  voted: false
square-twitter:
  aliases:
    names:
      - twitter-square
  changes:
    - 1.0.0
    - 5.0.0
    - 6.1.2
  label: Twitter Square
  search:
    terms: []
  styles:
    - brands
  unicode: f081
  voted: false
square-up-right:
  aliases:
    names:
      - external-link-square-alt
    unicodes:
      composite:
        - '2197'
      secondary:
        - 10f360
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square up right
  search:
    terms: []
  styles:
    - solid
  unicode: f360
  voted: false
square-viadeo:
  aliases:
    names:
      - viadeo-square
  changes:
    - 4.6.0
    - 5.0.0
    - 5.7.0
    - 6.1.2
  label: Viadeo Square
  search:
    terms: []
  styles:
    - brands
  unicode: f2aa
  voted: false
square-vimeo:
  aliases:
    names:
      - vimeo-square
  changes:
    - 4.0.0
    - 5.0.0
    - 6.1.2
  label: Vimeo Square
  search:
    terms: []
  styles:
    - brands
  unicode: f194
  voted: false
square-virus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Square Virus
  search:
    terms: []
  styles:
    - solid
  unicode: e578
  voted: false
square-whatsapp:
  aliases:
    names:
      - whatsapp-square
  changes:
    - 5.0.0
    - 6.1.2
  label: What's App Square
  search:
    terms: []
  styles:
    - brands
  unicode: f40c
  voted: false
square-xing:
  aliases:
    names:
      - xing-square
  changes:
    - 3.2.0
    - 5.0.0
    - 6.1.2
  label: Xing Square
  search:
    terms: []
  styles:
    - brands
  unicode: f169
  voted: false
square-xmark:
  aliases:
    names:
      - times-square
      - xmark-square
    unicodes:
      composite:
        - 274e
      secondary:
        - 10f2d3
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Square X Mark
  search:
    terms: []
  styles:
    - solid
  unicode: f2d3
  voted: false
square-youtube:
  aliases:
    names:
      - youtube-square
    unicodes:
      composite:
        - f166
  changes:
    - 5.0.3
    - 6.1.2
  label: YouTube Square
  search:
    terms: []
  styles:
    - brands
  unicode: f431
  voted: false
squarespace:
  changes:
    - 5.1.0
  label: Squarespace
  search:
    terms: []
  styles:
    - brands
  unicode: f5be
  voted: true
stack-exchange:
  changes:
    - 4.0.0
    - 5.0.0
    - 5.0.3
  label: Stack Exchange
  search:
    terms: []
  styles:
    - brands
  unicode: f18d
  voted: false
stack-overflow:
  changes:
    - 3.2.0
    - 5.0.0
  label: Stack Overflow
  search:
    terms: []
  styles:
    - brands
  unicode: f16c
  voted: false
stackpath:
  changes:
    - 5.8.2
  label: Stackpath
  search:
    terms: []
  styles:
    - brands
  unicode: f842
  voted: false
staff-snake:
  aliases:
    names:
      - rod-asclepius
      - rod-snake
      - staff-aesculapius
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Staff Aesculapius
  search:
    terms: []
  styles:
    - solid
  unicode: e579
  voted: false
stairs:
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Stairs
  search:
    terms: []
  styles:
    - solid
  unicode: e289
  voted: true
stamp:
  aliases:
    unicodes:
      secondary:
        - 10f5bf
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Stamp
  search:
    terms: []
  styles:
    - solid
  unicode: f5bf
  voted: false
stapler:
  changes:
    - 6.1.2
    - 6.2.0
  label: Stapler
  search:
    terms: []
  styles:
    - solid
  unicode: e5af
  voted: false
star:
  aliases:
    unicodes:
      composite:
        - 2b50
        - f006
      secondary:
        - 10f005
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Star
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f005
  voted: false
star-and-crescent:
  aliases:
    unicodes:
      composite:
        - 262a
      secondary:
        - 10f699
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Star and Crescent
  search:
    terms: []
  styles:
    - solid
  unicode: f699
  voted: false
star-half:
  aliases:
    unicodes:
      composite:
        - f123
      secondary:
        - 10f089
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: star-half
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f089
  voted: false
star-half-stroke:
  aliases:
    names:
      - star-half-alt
    unicodes:
      secondary:
        - 10f5c0
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Star half stroke
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f5c0
  voted: true
star-of-david:
  aliases:
    unicodes:
      composite:
        - '2721'
      secondary:
        - 10f69a
  changes:
    - 5.3.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Star of David
  search:
    terms: []
  styles:
    - solid
  unicode: f69a
  voted: false
star-of-life:
  aliases:
    unicodes:
      secondary:
        - 10f621
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Star of Life
  search:
    terms: []
  styles:
    - solid
  unicode: f621
  voted: false
staylinked:
  changes:
    - 5.0.0
  label: StayLinked
  search:
    terms: []
  styles:
    - brands
  unicode: f3f5
  voted: false
steam:
  changes:
    - 4.1.0
    - 5.0.0
  label: Steam
  search:
    terms: []
  styles:
    - brands
  unicode: f1b6
  voted: false
steam-symbol:
  changes:
    - 5.0.0
  label: Steam Symbol
  search:
    terms: []
  styles:
    - brands
  unicode: f3f6
  voted: false
sterling-sign:
  aliases:
    names:
      - gbp
      - pound-sign
    unicodes:
      composite:
        - a3
      secondary:
        - 10f154
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Sterling sign
  search:
    terms: []
  styles:
    - solid
  unicode: f154
  voted: false
stethoscope:
  aliases:
    unicodes:
      composite:
        - 1fa7a
      secondary:
        - 10f0f1
  changes:
    - 3.0.0
    - 5.0.0
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Stethoscope
  search:
    terms: []
  styles:
    - solid
  unicode: f0f1
  voted: false
sticker-mule:
  changes:
    - 5.0.0
    - 5.7.0
  label: Sticker Mule
  search:
    terms: []
  styles:
    - brands
  unicode: f3f7
  voted: false
stop:
  aliases:
    unicodes:
      composite:
        - 23f9
      secondary:
        - 10f04d
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: stop
  search:
    terms: []
  styles:
    - solid
  unicode: f04d
  voted: false
stopwatch:
  aliases:
    unicodes:
      composite:
        - 23f1
      secondary:
        - 10f2f2
  changes:
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Stopwatch
  search:
    terms: []
  styles:
    - solid
  unicode: f2f2
  voted: false
stopwatch-20:
  aliases:
    unicodes:
      secondary:
        - 10e06f
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Stopwatch 20
  search:
    terms: []
  styles:
    - solid
  unicode: e06f
  voted: false
store:
  aliases:
    unicodes:
      secondary:
        - 10f54e
  changes:
    - 5.0.13
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Store
  search:
    terms: []
  styles:
    - solid
  unicode: f54e
  voted: true
store-slash:
  aliases:
    unicodes:
      secondary:
        - '10e071'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Store Slash
  search:
    terms: []
  styles:
    - solid
  unicode: e071
  voted: false
strava:
  changes:
    - 5.0.0
    - 5.0.1
    - 5.7.0
    - 5.8.0
  label: Strava
  search:
    terms: []
  styles:
    - brands
  unicode: f428
  voted: false
street-view:
  aliases:
    unicodes:
      secondary:
        - 10f21d
  changes:
    - 4.3.0
    - 5.0.0
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Street View
  search:
    terms: []
  styles:
    - solid
  unicode: f21d
  voted: false
strikethrough:
  aliases:
    unicodes:
      secondary:
        - 10f0cc
  changes:
    - 2.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Strikethrough
  search:
    terms: []
  styles:
    - solid
  unicode: f0cc
  voted: false
stripe:
  changes:
    - 5.0.0
    - 5.0.3
  label: Stripe
  search:
    terms: []
  styles:
    - brands
  unicode: f429
  voted: false
stripe-s:
  changes:
    - 5.0.1
    - 5.8.0
  label: Stripe S
  search:
    terms: []
  styles:
    - brands
  unicode: f42a
  voted: false
stroopwafel:
  aliases:
    unicodes:
      secondary:
        - 10f551
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Stroopwafel
  search:
    terms: []
  styles:
    - solid
  unicode: f551
  voted: false
studiovinari:
  changes:
    - 5.0.0
  label: Studio Vinari
  search:
    terms: []
  styles:
    - brands
  unicode: f3f8
  voted: false
stumbleupon:
  changes:
    - 4.1.0
    - 5.0.0
  label: StumbleUpon Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f1a4
  voted: false
stumbleupon-circle:
  changes:
    - 4.1.0
    - 5.0.0
  label: StumbleUpon Circle
  search:
    terms: []
  styles:
    - brands
  unicode: f1a3
  voted: false
subscript:
  aliases:
    unicodes:
      secondary:
        - 10f12c
  changes:
    - 3.1.0
    - 5.0.0
    - 5.9.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: subscript
  search:
    terms: []
  styles:
    - solid
  unicode: f12c
  voted: false
suitcase:
  aliases:
    unicodes:
      composite:
        - 1f9f3
      secondary:
        - 10f0f2
  changes:
    - 3.0.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Suitcase
  search:
    terms: []
  styles:
    - solid
  unicode: f0f2
  voted: false
suitcase-medical:
  aliases:
    names:
      - medkit
    unicodes:
      secondary:
        - 10f0fa
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Suitcase medical
  search:
    terms: []
  styles:
    - solid
  unicode: f0fa
  voted: false
suitcase-rolling:
  aliases:
    unicodes:
      secondary:
        - 10f5c1
  changes:
    - 5.1.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Suitcase Rolling
  search:
    terms: []
  styles:
    - solid
  unicode: f5c1
  voted: false
sun:
  aliases:
    unicodes:
      composite:
        - '2600'
      secondary:
        - 10f185
  changes:
    - 3.2.0
    - 5.0.0
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Sun
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f185
  voted: false
sun-plant-wilt:
  changes:
    - 6.1.0
    - 6.2.0
  label: Sun Plant-wilt
  search:
    terms: []
  styles:
    - solid
  unicode: e57a
  voted: false
superpowers:
  changes:
    - 4.7.0
    - 5.0.0
  label: Superpowers
  search:
    terms: []
  styles:
    - brands
  unicode: f2dd
  voted: false
superscript:
  aliases:
    unicodes:
      secondary:
        - 10f12b
  changes:
    - 3.1.0
    - 5.0.0
    - 5.9.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: superscript
  search:
    terms: []
  styles:
    - solid
  unicode: f12b
  voted: false
supple:
  changes:
    - 5.0.0
  label: Supple
  search:
    terms: []
  styles:
    - brands
  unicode: f3f9
  voted: false
suse:
  changes:
    - 5.6.0
    - 5.8.0
  label: Suse
  search:
    terms: []
  styles:
    - brands
  unicode: f7d6
  voted: true
swatchbook:
  aliases:
    unicodes:
      secondary:
        - 10f5c3
  changes:
    - 5.1.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Swatchbook
  search:
    terms: []
  styles:
    - solid
  unicode: f5c3
  voted: false
swift:
  changes:
    - 5.11.0
  label: Swift
  search:
    terms: []
  styles:
    - brands
  unicode: f8e1
  voted: false
symfony:
  changes:
    - 5.8.0
  label: Symfony
  search:
    terms: []
  styles:
    - brands
  unicode: f83d
  voted: false
synagogue:
  aliases:
    unicodes:
      composite:
        - 1f54d
      secondary:
        - 10f69b
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Synagogue
  search:
    terms: []
  styles:
    - solid
  unicode: f69b
  voted: false
syringe:
  aliases:
    unicodes:
      composite:
        - 1f489
      secondary:
        - 10f48e
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Syringe
  search:
    terms: []
  styles:
    - solid
  unicode: f48e
  voted: false
t:
  aliases:
    unicodes:
      composite:
        - '74'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: T
  search:
    terms: []
  styles:
    - solid
  unicode: '54'
  voted: false
table:
  aliases:
    unicodes:
      secondary:
        - 10f0ce
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: table
  search:
    terms: []
  styles:
    - solid
  unicode: f0ce
  voted: false
table-cells:
  aliases:
    names:
      - th
    unicodes:
      secondary:
        - 10f00a
  changes:
    - 1.0.0
    - 5.0.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Table cells
  search:
    terms: []
  styles:
    - solid
  unicode: f00a
  voted: false
table-cells-large:
  aliases:
    names:
      - th-large
    unicodes:
      secondary:
        - 10f009
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Table cells large
  search:
    terms: []
  styles:
    - solid
  unicode: f009
  voted: false
table-columns:
  aliases:
    names:
      - columns
    unicodes:
      secondary:
        - 10f0db
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Table columns
  search:
    terms: []
  styles:
    - solid
  unicode: f0db
  voted: false
table-list:
  aliases:
    names:
      - th-list
    unicodes:
      secondary:
        - 10f00b
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Table list
  search:
    terms: []
  styles:
    - solid
  unicode: f00b
  voted: false
table-tennis-paddle-ball:
  aliases:
    names:
      - ping-pong-paddle-ball
      - table-tennis
    unicodes:
      composite:
        - 1f3d3
      secondary:
        - 10f45d
  changes:
    - 5.0.5
    - 6.0.0-beta1
    - 6.2.0
  label: Table tennis paddle ball
  search:
    terms: []
  styles:
    - solid
  unicode: f45d
  voted: false
tablet:
  aliases:
    names:
      - tablet-android
    unicodes:
      secondary:
        - 10f3fb
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Tablet
  search:
    terms: []
  styles:
    - solid
  unicode: f3fb
  voted: false
tablet-button:
  aliases:
    unicodes:
      secondary:
        - 10f10a
  changes:
    - 3.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Tablet button
  search:
    terms: []
  styles:
    - solid
  unicode: f10a
  voted: false
tablet-screen-button:
  aliases:
    names:
      - tablet-alt
    unicodes:
      secondary:
        - 10f3fa
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Tablet screen button
  search:
    terms: []
  styles:
    - solid
  unicode: f3fa
  voted: false
tablets:
  aliases:
    unicodes:
      secondary:
        - 10f490
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Tablets
  search:
    terms: []
  styles:
    - solid
  unicode: f490
  voted: false
tachograph-digital:
  aliases:
    names:
      - digital-tachograph
    unicodes:
      secondary:
        - 10f566
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Tachograph digital
  search:
    terms: []
  styles:
    - solid
  unicode: f566
  voted: true
tag:
  aliases:
    unicodes:
      composite:
        - 1f3f7
      secondary:
        - 10f02b
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: tag
  search:
    terms: []
  styles:
    - solid
  unicode: f02b
  voted: false
tags:
  aliases:
    unicodes:
      secondary:
        - 10f02c
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: tags
  search:
    terms: []
  styles:
    - solid
  unicode: f02c
  voted: false
tape:
  aliases:
    unicodes:
      secondary:
        - 10f4db
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Tape
  search:
    terms: []
  styles:
    - solid
  unicode: f4db
  voted: false
tarp:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Tarp
  search:
    terms: []
  styles:
    - solid
  unicode: e57b
  voted: false
tarp-droplet:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Tarp Droplet
  search:
    terms: []
  styles:
    - solid
  unicode: e57c
  voted: false
taxi:
  aliases:
    names:
      - cab
    unicodes:
      composite:
        - 1f696
      secondary:
        - 10f1ba
  changes:
    - 4.1.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Taxi
  search:
    terms: []
  styles:
    - solid
  unicode: f1ba
  voted: false
teamspeak:
  changes:
    - 5.0.11
    - 5.1.0
    - 5.8.0
  label: TeamSpeak
  search:
    terms: []
  styles:
    - brands
  unicode: f4f9
  voted: true
teeth:
  aliases:
    unicodes:
      secondary:
        - 10f62e
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Teeth
  search:
    terms: []
  styles:
    - solid
  unicode: f62e
  voted: false
teeth-open:
  aliases:
    unicodes:
      secondary:
        - 10f62f
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.2.0
  label: Teeth Open
  search:
    terms: []
  styles:
    - solid
  unicode: f62f
  voted: false
telegram:
  aliases:
    names:
      - telegram-plane
    unicodes:
      composite:
        - f3fe
      secondary:
        - 10f3fe
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
  label: Telegram
  search:
    terms: []
  styles:
    - brands
  unicode: f2c6
  voted: false
temperature-arrow-down:
  aliases:
    names:
      - temperature-down
    unicodes:
      secondary:
        - 10e03f
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Temperature arrow down
  search:
    terms: []
  styles:
    - solid
  unicode: e03f
  voted: false
temperature-arrow-up:
  aliases:
    names:
      - temperature-up
    unicodes:
      secondary:
        - '10e040'
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Temperature arrow up
  search:
    terms: []
  styles:
    - solid
  unicode: e040
  voted: false
temperature-empty:
  aliases:
    names:
      - temperature-0
      - thermometer-0
      - thermometer-empty
    unicodes:
      secondary:
        - 10f2cb
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Temperature empty
  search:
    terms: []
  styles:
    - solid
  unicode: f2cb
  voted: false
temperature-full:
  aliases:
    names:
      - temperature-4
      - thermometer-4
      - thermometer-full
    unicodes:
      secondary:
        - 10f2c7
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Temperature full
  search:
    terms: []
  styles:
    - solid
  unicode: f2c7
  voted: false
temperature-half:
  aliases:
    names:
      - temperature-2
      - thermometer-2
      - thermometer-half
    unicodes:
      composite:
        - 1f321
      secondary:
        - 10f2c9
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Temperature half
  search:
    terms: []
  styles:
    - solid
  unicode: f2c9
  voted: false
temperature-high:
  aliases:
    unicodes:
      secondary:
        - 10f769
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: High Temperature
  search:
    terms: []
  styles:
    - solid
  unicode: f769
  voted: false
temperature-low:
  aliases:
    unicodes:
      secondary:
        - 10f76b
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Low Temperature
  search:
    terms: []
  styles:
    - solid
  unicode: f76b
  voted: false
temperature-quarter:
  aliases:
    names:
      - temperature-1
      - thermometer-1
      - thermometer-quarter
    unicodes:
      secondary:
        - 10f2ca
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Temperature quarter
  search:
    terms: []
  styles:
    - solid
  unicode: f2ca
  voted: false
temperature-three-quarters:
  aliases:
    names:
      - temperature-3
      - thermometer-3
      - thermometer-three-quarters
    unicodes:
      secondary:
        - 10f2c8
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Temperature three quarters
  search:
    terms: []
  styles:
    - solid
  unicode: f2c8
  voted: false
tencent-weibo:
  changes:
    - 4.1.0
    - 5.0.0
  label: Tencent Weibo
  search:
    terms: []
  styles:
    - brands
  unicode: f1d5
  voted: false
tenge-sign:
  aliases:
    names:
      - tenge
    unicodes:
      composite:
        - 20b8
      secondary:
        - 10f7d7
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Tenge sign
  search:
    terms: []
  styles:
    - solid
  unicode: f7d7
  voted: true
tent:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tent
  search:
    terms: []
  styles:
    - solid
  unicode: e57d
  voted: false
tent-arrow-down-to-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tent Arrow-down-to-line
  search:
    terms: []
  styles:
    - solid
  unicode: e57e
  voted: false
tent-arrow-left-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tent Arrow-left-right
  search:
    terms: []
  styles:
    - solid
  unicode: e57f
  voted: false
tent-arrow-turn-left:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tent Arrow-turn-left
  search:
    terms: []
  styles:
    - solid
  unicode: e580
  voted: false
tent-arrows-down:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tent Arrows-down
  search:
    terms: []
  styles:
    - solid
  unicode: e581
  voted: false
tents:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tents
  search:
    terms: []
  styles:
    - solid
  unicode: e582
  voted: false
terminal:
  aliases:
    unicodes:
      secondary:
        - 10f120
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Terminal
  search:
    terms: []
  styles:
    - solid
  unicode: f120
  voted: false
text-height:
  aliases:
    unicodes:
      secondary:
        - 10f034
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: text-height
  search:
    terms: []
  styles:
    - solid
  unicode: f034
  voted: false
text-slash:
  aliases:
    names:
      - remove-format
    unicodes:
      secondary:
        - 10f87d
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Text slash
  search:
    terms: []
  styles:
    - solid
  unicode: f87d
  voted: false
text-width:
  aliases:
    unicodes:
      secondary:
        - 10f035
  changes:
    - 1.0.0
    - 5.0.0
    - 5.9.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Text Width
  search:
    terms: []
  styles:
    - solid
  unicode: f035
  voted: false
the-red-yeti:
  changes:
    - 5.3.0
    - 5.7.0
    - 5.8.0
  label: The Red Yeti
  search:
    terms: []
  styles:
    - brands
  unicode: f69d
  voted: false
themeco:
  changes:
    - 5.1.0
    - 5.8.0
  label: Themeco
  search:
    terms: []
  styles:
    - brands
  unicode: f5c6
  voted: false
themeisle:
  changes:
    - 4.6.0
    - 5.0.0
  label: ThemeIsle
  search:
    terms: []
  styles:
    - brands
  unicode: f2b2
  voted: false
thermometer:
  aliases:
    unicodes:
      secondary:
        - 10f491
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Thermometer
  search:
    terms: []
  styles:
    - solid
  unicode: f491
  voted: false
think-peaks:
  changes:
    - 5.4.2
    - 5.8.0
  label: Think Peaks
  search:
    terms: []
  styles:
    - brands
  unicode: f731
  voted: false
thumbs-down:
  aliases:
    unicodes:
      composite:
        - 1f44e
        - f088
      secondary:
        - 10f165
  changes:
    - 3.2.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: thumbs-down
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f165
  voted: false
thumbs-up:
  aliases:
    unicodes:
      composite:
        - 1f44d
        - f087
      secondary:
        - 10f164
  changes:
    - 3.2.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: thumbs-up
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f164
  voted: false
thumbtack:
  aliases:
    names:
      - thumb-tack
    unicodes:
      composite:
        - 1f4cc
        - 1f588
      secondary:
        - 10f08d
  changes:
    - 1.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Thumbtack
  search:
    terms: []
  styles:
    - solid
  unicode: f08d
  voted: false
ticket:
  aliases:
    unicodes:
      composite:
        - 1f39f
      secondary:
        - 10f145
  changes:
    - 3.1.0
    - 5.0.0
    - 5.10.1
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Ticket
  search:
    terms: []
  styles:
    - solid
  unicode: f145
  voted: false
ticket-simple:
  aliases:
    names:
      - ticket-alt
    unicodes:
      secondary:
        - 10f3ff
  changes:
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Ticket simple
  search:
    terms: []
  styles:
    - solid
  unicode: f3ff
  voted: false
tiktok:
  changes:
    - 5.13.1
    - 5.14.0
  label: TikTok
  search:
    terms: []
  styles:
    - brands
  unicode: e07b
  voted: true
timeline:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Timeline
  search:
    terms: []
  styles:
    - solid
  unicode: e29c
  voted: true
toggle-off:
  aliases:
    unicodes:
      secondary:
        - 10f204
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.1.2
    - 6.2.0
  label: Toggle Off
  search:
    terms: []
  styles:
    - solid
  unicode: f204
  voted: false
toggle-on:
  aliases:
    unicodes:
      secondary:
        - 10f205
  changes:
    - 4.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.1.2
    - 6.2.0
  label: Toggle On
  search:
    terms: []
  styles:
    - solid
  unicode: f205
  voted: false
toilet:
  aliases:
    unicodes:
      composite:
        - 1f6bd
      secondary:
        - 10f7d8
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Toilet
  search:
    terms: []
  styles:
    - solid
  unicode: f7d8
  voted: true
toilet-paper:
  aliases:
    unicodes:
      composite:
        - 1f9fb
      secondary:
        - 10f71e
  changes:
    - 5.4.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Toilet Paper
  search:
    terms: []
  styles:
    - solid
  unicode: f71e
  voted: false
toilet-paper-slash:
  aliases:
    unicodes:
      secondary:
        - '10e072'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Toilet Paper Slash
  search:
    terms: []
  styles:
    - solid
  unicode: e072
  voted: false
toilet-portable:
  changes:
    - 6.1.0
    - 6.2.0
  label: Toilet Portable
  search:
    terms: []
  styles:
    - solid
  unicode: e583
  voted: false
toilets-portable:
  changes:
    - 6.1.0
    - 6.2.0
  label: Toilets Portable
  search:
    terms: []
  styles:
    - solid
  unicode: e584
  voted: false
toolbox:
  aliases:
    unicodes:
      composite:
        - 1f9f0
      secondary:
        - 10f552
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Toolbox
  search:
    terms: []
  styles:
    - solid
  unicode: f552
  voted: true
tooth:
  aliases:
    unicodes:
      composite:
        - 1f9b7
      secondary:
        - 10f5c9
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Tooth
  search:
    terms: []
  styles:
    - solid
  unicode: f5c9
  voted: true
torii-gate:
  aliases:
    unicodes:
      composite:
        - '26e9'
      secondary:
        - 10f6a1
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Torii Gate
  search:
    terms: []
  styles:
    - solid
  unicode: f6a1
  voted: false
tornado:
  aliases:
    unicodes:
      composite:
        - 1f32a
      secondary:
        - 10f76f
  changes:
    - 5.5.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Tornado
  search:
    terms: []
  styles:
    - solid
  unicode: f76f
  voted: false
tower-broadcast:
  aliases:
    names:
      - broadcast-tower
    unicodes:
      secondary:
        - 10f519
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Tower broadcast
  search:
    terms: []
  styles:
    - solid
  unicode: f519
  voted: true
tower-cell:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tower Cell
  search:
    terms: []
  styles:
    - solid
  unicode: e585
  voted: false
tower-observation:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tower Observation
  search:
    terms: []
  styles:
    - solid
  unicode: e586
  voted: false
tractor:
  aliases:
    unicodes:
      composite:
        - 1f69c
      secondary:
        - 10f722
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Tractor
  search:
    terms: []
  styles:
    - solid
  unicode: f722
  voted: false
trade-federation:
  changes:
    - 5.0.12
  label: Trade Federation
  search:
    terms: []
  styles:
    - brands
  unicode: f513
  voted: false
trademark:
  aliases:
    unicodes:
      composite:
        - '2122'
      secondary:
        - 10f25c
  changes:
    - 4.4.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Trademark
  search:
    terms: []
  styles:
    - solid
  unicode: f25c
  voted: false
traffic-light:
  aliases:
    unicodes:
      composite:
        - 1f6a6
      secondary:
        - 10f637
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Traffic Light
  search:
    terms: []
  styles:
    - solid
  unicode: f637
  voted: false
trailer:
  aliases:
    unicodes:
      secondary:
        - '10e041'
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Trailer
  search:
    terms: []
  styles:
    - solid
  unicode: e041
  voted: true
train:
  aliases:
    unicodes:
      composite:
        - 1f686
      secondary:
        - 10f238
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Train
  search:
    terms: []
  styles:
    - solid
  unicode: f238
  voted: false
train-subway:
  aliases:
    names:
      - subway
    unicodes:
      secondary:
        - 10f239
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Train subway
  search:
    terms: []
  styles:
    - solid
  unicode: f239
  voted: false
train-tram:
  aliases:
    unicodes:
      composite:
        - 1f68a
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.2
    - 6.2.0
  label: Train tram
  search:
    terms: []
  styles:
    - solid
  unicode: e5b4
  voted: false
transgender:
  aliases:
    names:
      - transgender-alt
    unicodes:
      composite:
        - 26a7
      secondary:
        - 10f225
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Transgender
  search:
    terms: []
  styles:
    - solid
  unicode: f225
  voted: false
trash:
  aliases:
    unicodes:
      secondary:
        - 10f1f8
  changes:
    - 4.2.0
    - 5.0.0
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Trash
  search:
    terms: []
  styles:
    - solid
  unicode: f1f8
  voted: false
trash-arrow-up:
  aliases:
    names:
      - trash-restore
    unicodes:
      secondary:
        - 10f829
  changes:
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Trash arrow up
  search:
    terms: []
  styles:
    - solid
  unicode: f829
  voted: true
trash-can:
  aliases:
    names:
      - trash-alt
    unicodes:
      composite:
        - f014
      secondary:
        - 10f2ed
  changes:
    - 5.0.0
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Trash can
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2ed
  voted: false
trash-can-arrow-up:
  aliases:
    names:
      - trash-restore-alt
    unicodes:
      secondary:
        - 10f82a
  changes:
    - 5.7.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Trash can arrow up
  search:
    terms: []
  styles:
    - solid
  unicode: f82a
  voted: true
tree:
  aliases:
    unicodes:
      composite:
        - 1f332
      secondary:
        - 10f1bb
  changes:
    - 4.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Tree
  search:
    terms: []
  styles:
    - solid
  unicode: f1bb
  voted: false
tree-city:
  changes:
    - 6.1.0
    - 6.2.0
  label: Tree City
  search:
    terms: []
  styles:
    - solid
  unicode: e587
  voted: false
trello:
  changes:
    - 3.2.0
    - 5.0.0
    - 5.6.0
  label: Trello
  search:
    terms: []
  styles:
    - brands
  unicode: f181
  voted: false
triangle-exclamation:
  aliases:
    names:
      - exclamation-triangle
      - warning
    unicodes:
      composite:
        - 26a0
      secondary:
        - 10f071
  changes:
    - 1.0.0
    - 5.0.0
    - 5.6.1
    - 6.0.0-beta1
    - 6.2.0
  label: Triangle exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: f071
  voted: false
trophy:
  aliases:
    unicodes:
      composite:
        - 1f3c6
      secondary:
        - 10f091
  changes:
    - 1.0.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: trophy
  search:
    terms: []
  styles:
    - solid
  unicode: f091
  voted: false
trowel:
  changes:
    - 6.1.0
    - 6.2.0
  label: Trowel
  search:
    terms: []
  styles:
    - solid
  unicode: e589
  voted: false
trowel-bricks:
  changes:
    - 6.1.0
    - 6.2.0
  label: Trowel Bricks
  search:
    terms: []
  styles:
    - solid
  unicode: e58a
  voted: false
truck:
  aliases:
    unicodes:
      composite:
        - 1f69a
        - 26df
      secondary:
        - 10f0d1
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.7
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: truck
  search:
    terms: []
  styles:
    - solid
  unicode: f0d1
  voted: false
truck-arrow-right:
  changes:
    - 6.1.0
    - 6.2.0
  label: Truck Arrow-right
  search:
    terms: []
  styles:
    - solid
  unicode: e58b
  voted: false
truck-droplet:
  changes:
    - 6.1.0
    - 6.2.0
  label: Truck Droplet
  search:
    terms: []
  styles:
    - solid
  unicode: e58c
  voted: false
truck-fast:
  aliases:
    names:
      - shipping-fast
    unicodes:
      secondary:
        - 10f48b
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Truck fast
  search:
    terms: []
  styles:
    - solid
  unicode: f48b
  voted: false
truck-field:
  changes:
    - 6.1.0
    - 6.2.0
  label: Truck Field
  search:
    terms: []
  styles:
    - solid
  unicode: e58d
  voted: false
truck-field-un:
  changes:
    - 6.1.0
    - 6.2.0
  label: Truck Field-un
  search:
    terms: []
  styles:
    - solid
  unicode: e58e
  voted: false
truck-front:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.1.0
    - 6.2.0
  label: Truck Front
  search:
    terms: []
  styles:
    - solid
  unicode: e2b7
  voted: false
truck-medical:
  aliases:
    names:
      - ambulance
    unicodes:
      composite:
        - 1f691
      secondary:
        - 10f0f9
  changes:
    - 3.0.0
    - 5.0.0
    - 5.0.7
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Truck medical
  search:
    terms: []
  styles:
    - solid
  unicode: f0f9
  voted: false
truck-monster:
  aliases:
    unicodes:
      secondary:
        - 10f63b
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Truck Monster
  search:
    terms: []
  styles:
    - solid
  unicode: f63b
  voted: false
truck-moving:
  aliases:
    unicodes:
      secondary:
        - 10f4df
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Truck Moving
  search:
    terms: []
  styles:
    - solid
  unicode: f4df
  voted: false
truck-pickup:
  aliases:
    unicodes:
      composite:
        - 1f6fb
      secondary:
        - 10f63c
  changes:
    - 5.2.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Truck Side
  search:
    terms: []
  styles:
    - solid
  unicode: f63c
  voted: false
truck-plane:
  changes:
    - 6.1.0
    - 6.2.0
  label: Truck Plane
  search:
    terms: []
  styles:
    - solid
  unicode: e58f
  voted: false
truck-ramp-box:
  aliases:
    names:
      - truck-loading
    unicodes:
      secondary:
        - 10f4de
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Truck ramp box
  search:
    terms: []
  styles:
    - solid
  unicode: f4de
  voted: false
tty:
  aliases:
    names:
      - teletype
    unicodes:
      secondary:
        - 10f1e4
  changes:
    - 4.2.0
    - 5.0.0
    - 5.7.0
    - 6.0.0-beta1
    - 6.2.0
  label: TTY
  search:
    terms: []
  styles:
    - solid
  unicode: f1e4
  voted: false
tumblr:
  changes:
    - 3.2.0
    - 5.0.0
  label: Tumblr
  search:
    terms: []
  styles:
    - brands
  unicode: f173
  voted: false
turkish-lira-sign:
  aliases:
    names:
      - try
      - turkish-lira
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Turkish Lira-sign
  search:
    terms: []
  styles:
    - solid
  unicode: e2bb
  voted: false
turn-down:
  aliases:
    names:
      - level-down-alt
    unicodes:
      composite:
        - '2935'
      secondary:
        - 10f3be
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Turn down
  search:
    terms: []
  styles:
    - solid
  unicode: f3be
  voted: false
turn-up:
  aliases:
    names:
      - level-up-alt
    unicodes:
      composite:
        - '2934'
      secondary:
        - 10f3bf
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Turn up
  search:
    terms: []
  styles:
    - solid
  unicode: f3bf
  voted: false
tv:
  aliases:
    names:
      - television
      - tv-alt
    unicodes:
      composite:
        - f8e5
      primary:
        - f8e5
      secondary:
        - 10f26c
        - 10f8e5
  changes:
    - 4.4.0
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Television
  search:
    terms: []
  styles:
    - solid
  unicode: f26c
  voted: false
twitch:
  changes:
    - 4.2.0
    - 5.0.0
    - 5.12.0
  label: Twitch
  search:
    terms: []
  styles:
    - brands
  unicode: f1e8
  voted: false
twitter:
  changes:
    - 2.0.0
    - 5.0.0
  label: Twitter
  search:
    terms: []
  styles:
    - brands
  unicode: f099
  voted: false
typo3:
  changes:
    - 5.0.1
    - 5.8.0
  label: Typo3
  search:
    terms: []
  styles:
    - brands
  unicode: f42b
  voted: false
u:
  aliases:
    unicodes:
      composite:
        - '75'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: U
  search:
    terms: []
  styles:
    - solid
  unicode: '55'
  voted: false
uber:
  changes:
    - 5.0.0
  label: Uber
  search:
    terms: []
  styles:
    - brands
  unicode: f402
  voted: false
ubuntu:
  changes:
    - 5.6.0
  label: Ubuntu
  search:
    terms: []
  styles:
    - brands
  unicode: f7df
  voted: true
uikit:
  changes:
    - 5.0.0
  label: UIkit
  search:
    terms: []
  styles:
    - brands
  unicode: f403
  voted: false
umbraco:
  changes:
    - 5.11.0
  label: Umbraco
  search:
    terms: []
  styles:
    - brands
  unicode: f8e8
  voted: false
umbrella:
  aliases:
    unicodes:
      secondary:
        - 10f0e9
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Umbrella
  search:
    terms: []
  styles:
    - solid
  unicode: f0e9
  voted: false
umbrella-beach:
  aliases:
    unicodes:
      composite:
        - 1f3d6
      secondary:
        - 10f5ca
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Umbrella Beach
  search:
    terms: []
  styles:
    - solid
  unicode: f5ca
  voted: false
uncharted:
  changes:
    - 5.15.0
  label: Uncharted Software
  search:
    terms: []
  styles:
    - brands
  unicode: e084
  voted: false
underline:
  aliases:
    unicodes:
      secondary:
        - 10f0cd
  changes:
    - 2.0.0
    - 5.0.0
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Underline
  search:
    terms: []
  styles:
    - solid
  unicode: f0cd
  voted: false
uniregistry:
  changes:
    - 5.0.0
  label: Uniregistry
  search:
    terms: []
  styles:
    - brands
  unicode: f404
  voted: false
unity:
  changes:
    - 5.12.0
    - 5.14.0
    - 6.0.0-beta3
  label: Unity 3D
  search:
    terms: []
  styles:
    - brands
  unicode: e049
  voted: true
universal-access:
  aliases:
    unicodes:
      secondary:
        - 10f29a
  changes:
    - 4.6.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Universal Access
  search:
    terms: []
  styles:
    - solid
  unicode: f29a
  voted: false
unlock:
  aliases:
    unicodes:
      composite:
        - 1f513
      secondary:
        - 10f09c
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: unlock
  search:
    terms: []
  styles:
    - solid
  unicode: f09c
  voted: false
unlock-keyhole:
  aliases:
    names:
      - unlock-alt
    unicodes:
      secondary:
        - 10f13e
  changes:
    - 3.1.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Unlock keyhole
  search:
    terms: []
  styles:
    - solid
  unicode: f13e
  voted: false
unsplash:
  changes:
    - 5.13.1
    - 5.14.0
  label: Unsplash
  search:
    terms: []
  styles:
    - brands
  unicode: e07c
  voted: false
untappd:
  changes:
    - 5.0.0
  label: Untappd
  search:
    terms: []
  styles:
    - brands
  unicode: f405
  voted: false
up-down:
  aliases:
    names:
      - arrows-alt-v
    unicodes:
      composite:
        - '2195'
        - 2b0d
      secondary:
        - 10f338
  changes:
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Up down
  search:
    terms: []
  styles:
    - solid
  unicode: f338
  voted: false
up-down-left-right:
  aliases:
    names:
      - arrows-alt
    unicodes:
      secondary:
        - 10f0b2
  changes:
    - 2.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Up down left right
  search:
    terms: []
  styles:
    - solid
  unicode: f0b2
  voted: false
up-long:
  aliases:
    names:
      - long-arrow-alt-up
    unicodes:
      secondary:
        - 10f30c
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Up long
  search:
    terms: []
  styles:
    - solid
  unicode: f30c
  voted: false
up-right-and-down-left-from-center:
  aliases:
    names:
      - expand-alt
    unicodes:
      secondary:
        - 10f424
  changes:
    - 1.0.0
    - 5.0.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Up right and down left from center
  search:
    terms: []
  styles:
    - solid
  unicode: f424
  voted: false
up-right-from-square:
  aliases:
    names:
      - external-link-alt
    unicodes:
      secondary:
        - 10f35d
  changes:
    - 5.0.0
    - 5.11.0
    - 6.0.0-beta1
    - 6.2.0
  label: Up right from square
  search:
    terms: []
  styles:
    - solid
  unicode: f35d
  voted: false
upload:
  aliases:
    unicodes:
      secondary:
        - 10f093
  changes:
    - 1.0.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Upload
  search:
    terms: []
  styles:
    - solid
  unicode: f093
  voted: false
ups:
  changes:
    - 5.6.0
    - 5.8.0
  label: UPS
  search:
    terms: []
  styles:
    - brands
  unicode: f7e0
  voted: false
usb:
  changes:
    - 4.5.0
    - 5.0.0
  label: USB
  search:
    terms: []
  styles:
    - brands
  unicode: f287
  voted: false
user:
  aliases:
    unicodes:
      composite:
        - 1f464
        - f2c0
      secondary:
        - 10f007
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.3
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f007
  voted: false
user-astronaut:
  aliases:
    unicodes:
      secondary:
        - 10f4fb
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Astronaut
  search:
    terms: []
  styles:
    - solid
  unicode: f4fb
  voted: false
user-check:
  aliases:
    unicodes:
      secondary:
        - 10f4fc
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Check
  search:
    terms: []
  styles:
    - solid
  unicode: f4fc
  voted: false
user-clock:
  aliases:
    unicodes:
      secondary:
        - 10f4fd
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Clock
  search:
    terms: []
  styles:
    - solid
  unicode: f4fd
  voted: false
user-doctor:
  aliases:
    names:
      - user-md
    unicodes:
      secondary:
        - 10f0f0
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.3
    - 5.0.7
    - 5.0.11
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.1.2
    - 6.2.0
  label: User doctor
  search:
    terms: []
  styles:
    - solid
  unicode: f0f0
  voted: false
user-gear:
  aliases:
    names:
      - user-cog
    unicodes:
      secondary:
        - 10f4fe
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User gear
  search:
    terms: []
  styles:
    - solid
  unicode: f4fe
  voted: false
user-graduate:
  aliases:
    unicodes:
      secondary:
        - 10f501
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Graduate
  search:
    terms: []
  styles:
    - solid
  unicode: f501
  voted: false
user-group:
  aliases:
    names:
      - user-friends
    unicodes:
      composite:
        - 1f465
      secondary:
        - 10f500
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User group
  search:
    terms: []
  styles:
    - solid
  unicode: f500
  voted: false
user-injured:
  aliases:
    unicodes:
      secondary:
        - 10f728
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: User Injured
  search:
    terms: []
  styles:
    - solid
  unicode: f728
  voted: false
user-large:
  aliases:
    names:
      - user-alt
    unicodes:
      secondary:
        - 10f406
  changes:
    - 5.0.0
    - 5.0.3
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User large
  search:
    terms: []
  styles:
    - solid
  unicode: f406
  voted: false
user-large-slash:
  aliases:
    names:
      - user-alt-slash
    unicodes:
      secondary:
        - 10f4fa
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User large slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4fa
  voted: false
user-lock:
  aliases:
    unicodes:
      secondary:
        - 10f502
  changes:
    - 5.0.11
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: User Lock
  search:
    terms: []
  styles:
    - solid
  unicode: f502
  voted: false
user-minus:
  aliases:
    unicodes:
      secondary:
        - 10f503
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Minus
  search:
    terms: []
  styles:
    - solid
  unicode: f503
  voted: false
user-ninja:
  aliases:
    unicodes:
      composite:
        - 1f977
      secondary:
        - 10f504
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Ninja
  search:
    terms: []
  styles:
    - solid
  unicode: f504
  voted: false
user-nurse:
  aliases:
    unicodes:
      secondary:
        - 10f82f
  changes:
    - 5.7.0
    - 5.12.0
    - 6.0.0-beta1
    - 6.2.0
  label: Nurse
  search:
    terms: []
  styles:
    - solid
  unicode: f82f
  voted: false
user-pen:
  aliases:
    names:
      - user-edit
    unicodes:
      secondary:
        - 10f4ff
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User pen
  search:
    terms: []
  styles:
    - solid
  unicode: f4ff
  voted: false
user-plus:
  aliases:
    unicodes:
      secondary:
        - 10f234
  changes:
    - 4.3.0
    - 5.0.0
    - 5.0.3
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Plus
  search:
    terms: []
  styles:
    - solid
  unicode: f234
  voted: false
user-secret:
  aliases:
    unicodes:
      composite:
        - 1f575
      secondary:
        - 10f21b
  changes:
    - 4.3.0
    - 5.0.0
    - 5.0.3
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Secret
  search:
    terms: []
  styles:
    - solid
  unicode: f21b
  voted: false
user-shield:
  aliases:
    unicodes:
      secondary:
        - 10f505
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Shield
  search:
    terms: []
  styles:
    - solid
  unicode: f505
  voted: false
user-slash:
  aliases:
    unicodes:
      secondary:
        - 10f506
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f506
  voted: false
user-tag:
  aliases:
    unicodes:
      secondary:
        - 10f507
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User Tag
  search:
    terms: []
  styles:
    - solid
  unicode: f507
  voted: false
user-tie:
  aliases:
    unicodes:
      secondary:
        - 10f508
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: User Tie
  search:
    terms: []
  styles:
    - solid
  unicode: f508
  voted: false
user-xmark:
  aliases:
    names:
      - user-times
    unicodes:
      secondary:
        - 10f235
  changes:
    - 4.3.0
    - 5.0.0
    - 5.0.3
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: User X Mark
  search:
    terms: []
  styles:
    - solid
  unicode: f235
  voted: false
users:
  aliases:
    unicodes:
      secondary:
        - 10f0c0
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.3
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: Users
  search:
    terms: []
  styles:
    - solid
  unicode: f0c0
  voted: false
users-between-lines:
  changes:
    - 6.1.0
    - 6.2.0
  label: Users Between-lines
  search:
    terms: []
  styles:
    - solid
  unicode: e591
  voted: false
users-gear:
  aliases:
    names:
      - users-cog
    unicodes:
      secondary:
        - 10f509
  changes:
    - 5.0.11
    - 6.0.0-beta1
    - 6.2.0
  label: Users gear
  search:
    terms: []
  styles:
    - solid
  unicode: f509
  voted: false
users-line:
  changes:
    - 6.1.0
    - 6.2.0
  label: Users Line
  search:
    terms: []
  styles:
    - solid
  unicode: e592
  voted: false
users-rays:
  changes:
    - 6.1.0
    - 6.2.0
  label: Users Rays
  search:
    terms: []
  styles:
    - solid
  unicode: e593
  voted: false
users-rectangle:
  changes:
    - 6.1.0
    - 6.2.0
  label: Users Rectangle
  search:
    terms: []
  styles:
    - solid
  unicode: e594
  voted: false
users-slash:
  aliases:
    unicodes:
      secondary:
        - '10e073'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Users Slash
  search:
    terms: []
  styles:
    - solid
  unicode: e073
  voted: false
users-viewfinder:
  changes:
    - 6.1.0
    - 6.2.0
  label: Users Viewfinder
  search:
    terms: []
  styles:
    - solid
  unicode: e595
  voted: false
usps:
  changes:
    - 5.6.0
    - 5.8.0
  label: United States Postal Service
  search:
    terms: []
  styles:
    - brands
  unicode: f7e1
  voted: false
ussunnah:
  changes:
    - 5.0.0
  label: us-Sunnah Foundation
  search:
    terms: []
  styles:
    - brands
  unicode: f407
  voted: false
utensils:
  aliases:
    names:
      - cutlery
    unicodes:
      composite:
        - 1f374
        - f0f5
      secondary:
        - 10f2e7
  changes:
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.2.0
  label: Utensils
  search:
    terms: []
  styles:
    - solid
  unicode: f2e7
  voted: false
v:
  aliases:
    unicodes:
      composite:
        - '76'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: V
  search:
    terms: []
  styles:
    - solid
  unicode: '56'
  voted: false
vaadin:
  changes:
    - 5.0.0
  label: Vaadin
  search:
    terms: []
  styles:
    - brands
  unicode: f408
  voted: false
van-shuttle:
  aliases:
    names:
      - shuttle-van
    unicodes:
      composite:
        - 1f690
      secondary:
        - 10f5b6
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0-beta2
    - 6.0.0-beta3
    - 6.2.0
  label: Van shuttle
  search:
    terms: []
  styles:
    - solid
  unicode: f5b6
  voted: false
vault:
  changes:
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Vault
  search:
    terms: []
  styles:
    - solid
  unicode: e2c5
  voted: false
vector-square:
  aliases:
    unicodes:
      secondary:
        - 10f5cb
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Vector Square
  search:
    terms: []
  styles:
    - solid
  unicode: f5cb
  voted: false
venus:
  aliases:
    unicodes:
      composite:
        - '2640'
      secondary:
        - 10f221
  changes:
    - 4.3.0
    - 5.0.0
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Venus
  search:
    terms: []
  styles:
    - solid
  unicode: f221
  voted: false
venus-double:
  aliases:
    unicodes:
      composite:
        - 26a2
      secondary:
        - 10f226
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Venus Double
  search:
    terms: []
  styles:
    - solid
  unicode: f226
  voted: false
venus-mars:
  aliases:
    unicodes:
      composite:
        - 26a4
      secondary:
        - 10f228
  changes:
    - 4.3.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.2.0
  label: Venus Mars
  search:
    terms: []
  styles:
    - solid
  unicode: f228
  voted: false
vest:
  aliases:
    unicodes:
      secondary:
        - '10e085'
  changes:
    - 5.15.0
    - 5.15.1
    - 6.0.0-beta1
    - 6.2.0
  label: vest
  search:
    terms: []
  styles:
    - solid
  unicode: e085
  voted: false
vest-patches:
  aliases:
    unicodes:
      secondary:
        - '10e086'
  changes:
    - 5.15.0
    - 5.15.1
    - 6.0.0-beta1
    - 6.2.0
  label: vest-patches
  search:
    terms: []
  styles:
    - solid
  unicode: e086
  voted: false
viacoin:
  changes:
    - 4.3.0
    - 5.0.0
  label: Viacoin
  search:
    terms: []
  styles:
    - brands
  unicode: f237
  voted: false
viadeo:
  changes:
    - 4.6.0
    - 5.0.0
  label: Viadeo
  search:
    terms: []
  styles:
    - brands
  unicode: f2a9
  voted: false
vial:
  aliases:
    unicodes:
      composite:
        - 1f9ea
      secondary:
        - 10f492
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Vial
  search:
    terms: []
  styles:
    - solid
  unicode: f492
  voted: false
vial-circle-check:
  changes:
    - 6.1.0
    - 6.2.0
  label: Vial Circle-check
  search:
    terms: []
  styles:
    - solid
  unicode: e596
  voted: false
vial-virus:
  changes:
    - 6.1.0
    - 6.2.0
  label: Vial Virus
  search:
    terms: []
  styles:
    - solid
  unicode: e597
  voted: false
vials:
  aliases:
    unicodes:
      secondary:
        - 10f493
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Vials
  search:
    terms: []
  styles:
    - solid
  unicode: f493
  voted: false
viber:
  changes:
    - 5.0.0
    - 5.0.3
  label: Viber
  search:
    terms: []
  styles:
    - brands
  unicode: f409
  voted: false
video:
  aliases:
    names:
      - video-camera
    unicodes:
      secondary:
        - 10f03d
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Video
  search:
    terms: []
  styles:
    - solid
  unicode: f03d
  voted: false
video-slash:
  aliases:
    unicodes:
      secondary:
        - 10f4e2
  changes:
    - 5.0.9
    - 6.0.0-beta1
    - 6.2.0
  label: Video Slash
  search:
    terms: []
  styles:
    - solid
  unicode: f4e2
  voted: false
vihara:
  aliases:
    unicodes:
      secondary:
        - 10f6a7
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Vihara
  search:
    terms: []
  styles:
    - solid
  unicode: f6a7
  voted: false
vimeo:
  changes:
    - 5.0.0
  label: Vimeo
  search:
    terms: []
  styles:
    - brands
  unicode: f40a
  voted: false
vimeo-v:
  changes:
    - 4.4.0
    - 5.0.0
  label: Vimeo
  search:
    terms: []
  styles:
    - brands
  unicode: f27d
  voted: false
vine:
  changes:
    - 4.1.0
    - 5.0.0
  label: Vine
  search:
    terms: []
  styles:
    - brands
  unicode: f1ca
  voted: false
virus:
  aliases:
    unicodes:
      secondary:
        - '10e074'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Virus
  search:
    terms: []
  styles:
    - solid
  unicode: e074
  voted: false
virus-covid:
  changes:
    - 6.0.0
    - 6.1.0
    - 6.2.0
  label: Virus Covid
  search:
    terms: []
  styles:
    - solid
  unicode: e4a8
  voted: false
virus-covid-slash:
  changes:
    - 6.0.0
    - 6.2.0
  label: Virus Covid-slash
  search:
    terms: []
  styles:
    - solid
  unicode: e4a9
  voted: false
virus-slash:
  aliases:
    unicodes:
      secondary:
        - '10e075'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.1.2
    - 6.2.0
  label: Virus Slash
  search:
    terms: []
  styles:
    - solid
  unicode: e075
  voted: false
viruses:
  aliases:
    unicodes:
      secondary:
        - '10e076'
  changes:
    - 5.13.0
    - 5.14.0
    - 6.0.0-beta1
    - 6.2.0
  label: Viruses
  search:
    terms: []
  styles:
    - solid
  unicode: e076
  voted: false
vk:
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta3
  label: VK
  search:
    terms: []
  styles:
    - brands
  unicode: f189
  voted: false
vnv:
  changes:
    - 5.0.0
  label: VNV
  search:
    terms: []
  styles:
    - brands
  unicode: f40b
  voted: false
voicemail:
  aliases:
    unicodes:
      secondary:
        - 10f897
  changes:
    - 5.9.0
    - 6.0.0-beta1
    - 6.2.0
  label: Voicemail
  search:
    terms: []
  styles:
    - solid
  unicode: f897
  voted: true
volcano:
  aliases:
    unicodes:
      composite:
        - 1f30b
      secondary:
        - 10f770
  changes:
    - 5.5.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Volcano
  search:
    terms: []
  styles:
    - solid
  unicode: f770
  voted: false
volleyball:
  aliases:
    names:
      - volleyball-ball
    unicodes:
      composite:
        - 1f3d0
      secondary:
        - 10f45f
  changes:
    - 5.0.5
    - 5.8.0
    - 6.0.0-beta1
    - 6.2.0
  label: Volleyball Ball
  search:
    terms: []
  styles:
    - solid
  unicode: f45f
  voted: false
volume-high:
  aliases:
    names:
      - volume-up
    unicodes:
      composite:
        - 1f50a
      secondary:
        - 10f028
  changes:
    - 1.0.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Volume high
  search:
    terms: []
  styles:
    - solid
  unicode: f028
  voted: false
volume-low:
  aliases:
    names:
      - volume-down
    unicodes:
      composite:
        - 1f508
      secondary:
        - 10f027
  changes:
    - 1.0.0
    - 5.0.0
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Volume low
  search:
    terms: []
  styles:
    - solid
  unicode: f027
  voted: false
volume-off:
  aliases:
    unicodes:
      secondary:
        - 10f026
  changes:
    - 1.0.0
    - 5.0.0
    - 5.3.0
    - 5.8.0
    - 6.0.0-beta1
    - 6.2.0
  label: Volume Off
  search:
    terms: []
  styles:
    - solid
  unicode: f026
  voted: false
volume-xmark:
  aliases:
    names:
      - volume-mute
      - volume-times
    unicodes:
      secondary:
        - 10f6a9
  changes:
    - 5.3.0
    - 6.0.0-beta1
    - 6.2.0
  label: Volume X Mark
  search:
    terms: []
  styles:
    - solid
  unicode: f6a9
  voted: true
vr-cardboard:
  aliases:
    unicodes:
      secondary:
        - 10f729
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Cardboard VR
  search:
    terms: []
  styles:
    - solid
  unicode: f729
  voted: true
vuejs:
  changes:
    - 5.0.0
  label: Vue.js
  search:
    terms: []
  styles:
    - brands
  unicode: f41f
  voted: false
w:
  aliases:
    unicodes:
      composite:
        - '77'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: W
  search:
    terms: []
  styles:
    - solid
  unicode: '57'
  voted: false
walkie-talkie:
  aliases:
    unicodes:
      secondary:
        - 10f8ef
  changes:
    - 5.11.0
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Walkie Talkie
  search:
    terms: []
  styles:
    - solid
  unicode: f8ef
  voted: false
wallet:
  aliases:
    unicodes:
      secondary:
        - 10f555
  changes:
    - 5.0.13
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Wallet
  search:
    terms: []
  styles:
    - solid
  unicode: f555
  voted: true
wand-magic:
  aliases:
    names:
      - magic
    unicodes:
      secondary:
        - 10f0d0
  changes:
    - 2.0.0
    - 5.0.0
    - 5.1.0
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Wand magic
  search:
    terms: []
  styles:
    - solid
  unicode: f0d0
  voted: false
wand-magic-sparkles:
  aliases:
    names:
      - magic-wand-sparkles
  changes:
    - 6.0.0-beta1
    - 6.0.0
    - 6.2.0
  label: Wand magic sparkles
  search:
    terms: []
  styles:
    - solid
  unicode: e2ca
  voted: false
wand-sparkles:
  aliases:
    unicodes:
      secondary:
        - 10f72b
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Wand sparkles
  search:
    terms: []
  styles:
    - solid
  unicode: f72b
  voted: false
warehouse:
  aliases:
    unicodes:
      secondary:
        - 10f494
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Warehouse
  search:
    terms: []
  styles:
    - solid
  unicode: f494
  voted: false
watchman-monitoring:
  changes:
    - 5.15.0
  label: Watchman Monitoring
  search:
    terms: []
  styles:
    - brands
  unicode: e087
  voted: false
water:
  aliases:
    unicodes:
      secondary:
        - 10f773
  changes:
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Water
  search:
    terms: []
  styles:
    - solid
  unicode: f773
  voted: false
water-ladder:
  aliases:
    names:
      - ladder-water
      - swimming-pool
    unicodes:
      secondary:
        - 10f5c5
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Water ladder
  search:
    terms: []
  styles:
    - solid
  unicode: f5c5
  voted: false
wave-square:
  aliases:
    unicodes:
      secondary:
        - 10f83e
  changes:
    - 5.8.0
    - 6.0.0-beta1
    - 6.2.0
  label: Square Wave
  search:
    terms: []
  styles:
    - solid
  unicode: f83e
  voted: false
waze:
  changes:
    - 5.8.0
  label: Waze
  search:
    terms: []
  styles:
    - brands
  unicode: f83f
  voted: false
weebly:
  changes:
    - 5.1.0
  label: Weebly
  search:
    terms: []
  styles:
    - brands
  unicode: f5cc
  voted: true
weibo:
  changes:
    - 3.2.0
    - 5.0.0
  label: Weibo
  search:
    terms: []
  styles:
    - brands
  unicode: f18a
  voted: false
weight-hanging:
  aliases:
    unicodes:
      secondary:
        - 10f5cd
  changes:
    - 5.1.0
    - 6.0.0-beta1
    - 6.2.0
  label: Hanging Weight
  search:
    terms: []
  styles:
    - solid
  unicode: f5cd
  voted: false
weight-scale:
  aliases:
    names:
      - weight
    unicodes:
      secondary:
        - 10f496
  changes:
    - 5.0.7
    - 6.0.0-beta1
    - 6.2.0
  label: Weight scale
  search:
    terms: []
  styles:
    - solid
  unicode: f496
  voted: false
weixin:
  changes:
    - 4.1.0
    - 5.0.0
    - 5.0.3
  label: Weixin (WeChat)
  search:
    terms: []
  styles:
    - brands
  unicode: f1d7
  voted: false
whatsapp:
  changes:
    - 4.3.0
    - 5.0.0
  label: What's App
  search:
    terms: []
  styles:
    - brands
  unicode: f232
  voted: false
wheat-awn:
  aliases:
    names:
      - wheat-alt
  changes:
    - 6.0.0-beta1
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Wheat awn
  search:
    terms: []
  styles:
    - solid
  unicode: e2cd
  voted: false
wheat-awn-circle-exclamation:
  changes:
    - 6.1.0
    - 6.1.2
    - 6.2.0
  label: Wheat Awn-circle-exclamation
  search:
    terms: []
  styles:
    - solid
  unicode: e598
  voted: false
wheelchair:
  aliases:
    unicodes:
      secondary:
        - 10f193
  changes:
    - 4.0.0
    - 5.0.0
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: Wheelchair
  search:
    terms: []
  styles:
    - solid
  unicode: f193
  voted: false
wheelchair-move:
  aliases:
    names:
      - wheelchair-alt
  changes:
    - 6.0.0-beta1
    - 6.1.0
    - 6.2.0
  label: Wheelchair Move
  search:
    terms: []
  styles:
    - solid
  unicode: e2ce
  voted: false
whiskey-glass:
  aliases:
    names:
      - glass-whiskey
    unicodes:
      composite:
        - 1f943
      secondary:
        - 10f7a0
  changes:
    - 5.6.0
    - 6.0.0-beta1
    - 6.2.0
  label: Whiskey glass
  search:
    terms: []
  styles:
    - solid
  unicode: f7a0
  voted: false
whmcs:
  changes:
    - 5.0.0
  label: WHMCS
  search:
    terms: []
  styles:
    - brands
  unicode: f40d
  voted: false
wifi:
  aliases:
    names:
      - wifi-3
      - wifi-strong
    unicodes:
      secondary:
        - 10f1eb
  changes:
    - 4.2.0
    - 5.0.0
    - 5.3.0
    - 5.10.1
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: WiFi
  search:
    terms: []
  styles:
    - solid
  unicode: f1eb
  voted: false
wikipedia-w:
  changes:
    - 4.4.0
    - 5.0.0
  label: Wikipedia W
  search:
    terms: []
  styles:
    - brands
  unicode: f266
  voted: false
wind:
  aliases:
    unicodes:
      secondary:
        - 10f72e
  changes:
    - 5.4.0
    - 5.5.0
    - 6.0.0-beta1
    - 6.2.0
  label: Wind
  search:
    terms: []
  styles:
    - solid
  unicode: f72e
  voted: false
window-maximize:
  aliases:
    unicodes:
      composite:
        - 1f5d6
      secondary:
        - 10f2d0
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Window Maximize
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d0
  voted: false
window-minimize:
  aliases:
    unicodes:
      composite:
        - 1f5d5
      secondary:
        - 10f2d1
  changes:
    - 4.7.0
    - 5.0.0
    - 5.10.1
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Window Minimize
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d1
  voted: false
window-restore:
  aliases:
    unicodes:
      secondary:
        - 10f2d2
  changes:
    - 4.7.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Window Restore
  search:
    terms: []
  styles:
    - solid
    - regular
  unicode: f2d2
  voted: false
windows:
  changes:
    - 3.2.0
    - 5.0.0
  label: Windows
  search:
    terms: []
  styles:
    - brands
  unicode: f17a
  voted: false
wine-bottle:
  aliases:
    unicodes:
      secondary:
        - 10f72f
  changes:
    - 5.4.0
    - 6.0.0-beta1
    - 6.2.0
  label: Wine Bottle
  search:
    terms: []
  styles:
    - solid
  unicode: f72f
  voted: false
wine-glass:
  aliases:
    unicodes:
      composite:
        - 1f377
      secondary:
        - 10f4e3
  changes:
    - 5.0.9
    - 5.1.0
    - 5.10.1
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Wine Glass
  search:
    terms: []
  styles:
    - solid
  unicode: f4e3
  voted: false
wine-glass-empty:
  aliases:
    names:
      - wine-glass-alt
    unicodes:
      secondary:
        - 10f5ce
  changes:
    - 5.1.0
    - 5.10.1
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Wine glass empty
  search:
    terms: []
  styles:
    - solid
  unicode: f5ce
  voted: false
wirsindhandwerk:
  aliases:
    names:
      - wsh
  changes:
    - 6.0.0-beta1
  label: wirsindhandwerk
  search:
    terms: []
  styles:
    - brands
  unicode: e2d0
  voted: false
wix:
  changes:
    - 5.1.0
  label: Wix
  search:
    terms: []
  styles:
    - brands
  unicode: f5cf
  voted: true
wizards-of-the-coast:
  changes:
    - 5.4.0
  label: Wizards of the Coast
  search:
    terms: []
  styles:
    - brands
  unicode: f730
  voted: false
wodu:
  changes:
    - 5.15.0
  label: Wodu
  search:
    terms: []
  styles:
    - brands
  unicode: e088
  voted: false
wolf-pack-battalion:
  changes:
    - 5.0.12
    - 5.8.0
  label: Wolf Pack Battalion
  search:
    terms: []
  styles:
    - brands
  unicode: f514
  voted: false
won-sign:
  aliases:
    names:
      - krw
      - won
    unicodes:
      composite:
        - 20a9
      secondary:
        - 10f159
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Won Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f159
  voted: false
wordpress:
  changes:
    - 4.1.0
    - 5.0.0
  label: WordPress Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f19a
  voted: false
wordpress-simple:
  changes:
    - 5.0.0
  label: Wordpress Simple
  search:
    terms: []
  styles:
    - brands
  unicode: f411
  voted: false
worm:
  changes:
    - 6.1.0
    - 6.2.0
  label: Worm
  search:
    terms: []
  styles:
    - solid
  unicode: e599
  voted: false
wpbeginner:
  changes:
    - 4.6.0
    - 5.0.0
  label: WPBeginner
  search:
    terms: []
  styles:
    - brands
  unicode: f297
  voted: false
wpexplorer:
  changes:
    - 4.7.0
    - 5.0.0
  label: WPExplorer
  search:
    terms: []
  styles:
    - brands
  unicode: f2de
  voted: false
wpforms:
  changes:
    - 4.6.0
    - 5.0.0
  label: WPForms
  search:
    terms: []
  styles:
    - brands
  unicode: f298
  voted: false
wpressr:
  aliases:
    names:
      - rendact
  changes:
    - 5.4.2
  label: wpressr
  search:
    terms: []
  styles:
    - brands
  unicode: f3e4
  voted: false
wrench:
  aliases:
    unicodes:
      composite:
        - 1f527
      secondary:
        - 10f0ad
  changes:
    - 2.0.0
    - 5.0.0
    - 5.0.13
    - 6.0.0-beta1
    - 6.2.0
  label: Wrench
  search:
    terms: []
  styles:
    - solid
  unicode: f0ad
  voted: false
x:
  aliases:
    unicodes:
      composite:
        - '78'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: X
  search:
    terms: []
  styles:
    - solid
  unicode: '58'
  voted: false
x-ray:
  aliases:
    unicodes:
      secondary:
        - 10f497
  changes:
    - 5.0.7
    - 5.10.2
    - 6.0.0-beta1
    - 6.2.0
  label: X-Ray
  search:
    terms: []
  styles:
    - solid
  unicode: f497
  voted: false
xbox:
  changes:
    - 5.0.0
  label: Xbox
  search:
    terms: []
  styles:
    - brands
  unicode: f412
  voted: false
xing:
  changes:
    - 3.2.0
    - 5.0.0
  label: Xing
  search:
    terms: []
  styles:
    - brands
  unicode: f168
  voted: false
xmark:
  aliases:
    names:
      - close
      - multiply
      - remove
      - times
    unicodes:
      composite:
        - 1f5d9
        - '2715'
        - '2716'
        - 274c
        - d7
      secondary:
        - 10f00d
  changes:
    - 1.0.0
    - 5.0.0
    - 5.0.13
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: X Mark
  search:
    terms: []
  styles:
    - solid
  unicode: f00d
  voted: false
xmarks-lines:
  changes:
    - 6.1.0
    - 6.2.0
  label: Xmarks Lines
  search:
    terms: []
  styles:
    - solid
  unicode: e59a
  voted: false
'y':
  aliases:
    unicodes:
      composite:
        - '79'
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: 'Y'
  search:
    terms: []
  styles:
    - solid
  unicode: '59'
  voted: false
y-combinator:
  changes:
    - 4.4.0
    - 5.0.0
  label: Y Combinator
  search:
    terms: []
  styles:
    - brands
  unicode: f23b
  voted: false
yahoo:
  changes:
    - 4.1.0
    - 5.0.0
    - 5.0.3
    - 5.13.1
  label: Yahoo Logo
  search:
    terms: []
  styles:
    - brands
  unicode: f19e
  voted: false
yammer:
  changes:
    - 5.8.0
    - 6.0.0-beta1
  label: Yammer
  search:
    terms: []
  styles:
    - brands
  unicode: f840
  voted: false
yandex:
  changes:
    - 5.0.0
  label: Yandex
  search:
    terms: []
  styles:
    - brands
  unicode: f413
  voted: false
yandex-international:
  changes:
    - 5.0.0
  label: Yandex International
  search:
    terms: []
  styles:
    - brands
  unicode: f414
  voted: false
yarn:
  changes:
    - 5.6.0
  label: Yarn
  search:
    terms: []
  styles:
    - brands
  unicode: f7e3
  voted: true
yelp:
  changes:
    - 4.2.0
    - 5.0.0
    - 5.7.0
    - 5.8.0
  label: Yelp
  search:
    terms: []
  styles:
    - brands
  unicode: f1e9
  voted: false
yen-sign:
  aliases:
    names:
      - cny
      - jpy
      - rmb
      - yen
    unicodes:
      composite:
        - a5
      secondary:
        - 10f157
  changes:
    - 3.2.0
    - 5.0.0
    - 6.0.0-beta1
    - 6.0.0-beta3
    - 6.2.0
  label: Yen Sign
  search:
    terms: []
  styles:
    - solid
  unicode: f157
  voted: false
yin-yang:
  aliases:
    unicodes:
      composite:
        - 262f
      secondary:
        - 10f6ad
  changes:
    - 5.3.0
    - 5.10.2
    - 5.11.0
    - 5.11.1
    - 6.0.0-beta1
    - 6.2.0
  label: Yin Yang
  search:
    terms: []
  styles:
    - solid
  unicode: f6ad
  voted: false
yoast:
  changes:
    - 4.6.0
    - 5.0.0
    - 5.0.3
  label: Yoast
  search:
    terms: []
  styles:
    - brands
  unicode: f2b1
  voted: false
youtube:
  aliases:
    unicodes:
      composite:
        - f16a
  changes:
    - 3.2.0
    - 5.0.0
  label: YouTube
  search:
    terms: []
  styles:
    - brands
  unicode: f167
  voted: false
z:
  aliases:
    unicodes:
      composite:
        - 7a
  changes:
    - 6.0.0-beta1
    - 6.2.0
  label: Z
  search:
    terms: []
  styles:
    - solid
  unicode: 5a
  voted: false
zhihu:
  changes:
    - 5.2.0
  label: Zhihu
  search:
    terms: []
  styles:
    - brands
  unicode: f63f
  voted: true
