.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
  color: #1f2c39 !important;
  border-bottom: #1f2c39 2px solid !important;
}
.el-button--text {
  color: #1f2c39 !important;
}
.el-button--primary:focus,
.el-button--primary:hover {
  color: white;
  border-color: rgba(31, 44, 57, 0.9) !important;
  background-color: rgba(31, 44, 57, 0.8) !important;
}
.el-dropdown-menu__item:focus,
.el-dropdown-menu__item:not(.is-disabled):hover {
  background-color: rgba(31, 44, 57, 0.2) !important;
  color: #1f2c39 !important;
}
.el-select-dropdown__item.selected {
  color: #1f2c39 !important;
}
input[type=text]:focus,
input[type=password]:focus,
input[type=email]:focus,
input[type=url]:focus,
input[type=number]:focus,
input[type=tel]:focus,
textarea:focus,
select:focus,
.vTextField:focus {
  border-color: #1f2c39 !important;
}
.el-button--primary {
  background-color: #1f2c39 !important;
  border-color: #1f2c39 !important;
}
tbody a:link,
tbody a:visited {
  color: #1f2c39 !important;
}
tbody tr:hover td,
tbody tr:hover th {
  background-color: rgba(31, 44, 57, 0.2) !important;
}
a:link,
a:visited {
  color: #1f2c39 !important;
}
.results {
  color: inherit !important;
}
.el-select-dropdown__item.hover,
.el-select-dropdown__item:hover {
  background-color: rgba(31, 44, 57, 0.2) !important;
}
.el-date-table td.today span {
  color: #1f2c39 !important;
}
.el-date-table td.available:hover {
  color: #1f2c39 !important;
}
.el-picker-panel__icon-btn:hover {
  color: rgba(31, 44, 57, 0.8) !important;
}
.el-range-editor.is-active,
.el-range-editor.is-active:hover {
  border-color: #1f2c39 !important;
}
.el-date-table td.end-date span,
.el-date-table td.start-date span {
  background-color: #1f2c39 !important;
}
.el-date-table td.in-range div,
.el-date-table td.in-range div:hover,
.el-date-table.is-week-mode .el-date-table__row.current div,
.el-date-table.is-week-mode .el-date-table__row:hover div {
  background-color: rgba(31, 44, 57, 0.2) !important;
}
.el-time-panel__btn.confirm {
  color: #1f2c39 !important;
}
.el-pagination.is-background .el-pager li:not(.disabled).active {
  color: white;
  background-color: #1f2c39 !important;
}
.el-pagination.is-background .el-pager li:not(.disabled):hover {
  color: #1f2c39 !important;
}
.el-tabs--border-card > .el-tabs__header .el-tabs__item:not(.is-disabled):hover {
  color: #1f2c39 !important;
}
.navbar {
  background-color: #1f2c39 !important;
}
.navbar .el-button:focus,
.navbar .el-button:hover {
  color: #1f2c39 !important;
}
.navbar .el-button,
.navbar .el-breadcrumb__item:last-child .el-breadcrumb__inner,
.navbar .el-breadcrumb__item:last-child .el-breadcrumb__inner a,
.navbar .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
.navbar .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
  color: #FFF;
}
.navbar .el-breadcrumb__inner,
.navbar .el-breadcrumb__separator {
  color: #FFF;
}
.logo-wrap {
  color: #484848;
}
.menu {
  background-color: #ffffff !important;
}
.menu .el-menu-item {
  color: #484848;
}
.menu .el-menu-item:hover {
  color: #484848;
}
.menu .el-menu .is-active {
  color: #1f2c39 !important;
}
.menu .el-menu-item-group,
.menu .el-menu-item {
  background-color: #ffffff !important;
}
.menu .el-menu-item:hover {
  background-color: #fff !important;
  color: #484848 !important;
}
.menu .el-menu-item i {
  color: inherit;
}
.menu .el-submenu__title .far,
.menu .el-submenu__title .fa,
.menu .el-submenu__title .fab,
.menu .el-submenu__title .fas {
  color: inherit;
}
.menu .el-submenu__title i {
  color: #484848 !important;
}
.menu .el-submenu__title {
  color: #484848 !important;
  background-color: #fff !important;
}
.menu .el-submenu__title:hover,
.menu .is-opened .el-submenu__title {
  color: #666 !important;
  background-color: #f2f3f7 !important;
}
.quick-card a:hover {
  color: rgba(31, 44, 57, 0.8) !important;
}
.module h2,
.module caption,
.inline-group h2,
.selector-chosen h2 {
  background: #FFF !important;
  color: #666 !important;
}
#changelist .actions {
  min-height: 38px;
}
.colM {
  overflow: auto;
}
.menu .el-menu-item {
  color: #484848 !important;
}
.menu .el-menu-item:hover {
  background-color: #e8e8e8 !important;
}
.navbar .el-button:focus,
.navbar .el-button:hover {
  color: rgba(255, 255, 255, 0.8) !important;
}
.logo-wrap {
  color: #FFF;
  background-color: #1f2c39 !important;
}
.navbar {
  background-color: #1f2c39 !important;
}
/*# sourceMappingURL=e-black.css.map */