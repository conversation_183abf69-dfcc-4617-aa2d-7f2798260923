# This file is distributed under the same license as the Django package.
#
# Translators:
# GunChleoc, 2021
# GunChleoc, 2015
# GunChleoc, 2015
msgid ""
msgstr ""
"Project-Id-Version: django\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2021-04-07 14:40+0200\n"
"PO-Revision-Date: 2021-12-24 18:40+0000\n"
"Last-Translator: GunChleoc\n"
"Language-Team: Gaelic, Scottish (http://www.transifex.com/django/django/"
"language/gd/)\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Language: gd\n"
"Plural-Forms: nplurals=4; plural=(n==1 || n==11) ? 0 : (n==2 || n==12) ? 1 : "
"(n > 2 && n < 20) ? 2 : 3;\n"

msgid "Humanize"
msgstr "Humanize"

#. Translators: Ordinal format for 11 (11th), 12 (12th), and 13 (13th).
msgctxt "ordinal 11, 12, 13"
msgid "{}th"
msgstr "{}mh"

#. Translators: Ordinal format when value ends with 0, e.g. 80th.
msgctxt "ordinal 0"
msgid "{}th"
msgstr "{}mh"

#. Translators: Ordinal format when value ends with 1, e.g. 81st, except 11.
msgctxt "ordinal 1"
msgid "{}st"
msgstr "{}d"

#. Translators: Ordinal format when value ends with 2, e.g. 82nd, except 12.
msgctxt "ordinal 2"
msgid "{}nd"
msgstr "{}na"

#. Translators: Ordinal format when value ends with 3, e.g. 83th, except 13.
msgctxt "ordinal 3"
msgid "{}rd"
msgstr "{}s"

#. Translators: Ordinal format when value ends with 4, e.g. 84th.
msgctxt "ordinal 4"
msgid "{}th"
msgstr "{}mh"

#. Translators: Ordinal format when value ends with 5, e.g. 85th.
msgctxt "ordinal 5"
msgid "{}th"
msgstr "{}mh"

#. Translators: Ordinal format when value ends with 6, e.g. 86th.
msgctxt "ordinal 6"
msgid "{}th"
msgstr "{}mh"

#. Translators: Ordinal format when value ends with 7, e.g. 87th.
msgctxt "ordinal 7"
msgid "{}th"
msgstr "{}mh"

#. Translators: Ordinal format when value ends with 8, e.g. 88th.
msgctxt "ordinal 8"
msgid "{}th"
msgstr "{}mh"

#. Translators: Ordinal format when value ends with 9, e.g. 89th.
msgctxt "ordinal 9"
msgid "{}th"
msgstr "{}mh"

#, python-format
msgid "%(value)s million"
msgid_plural "%(value)s million"
msgstr[0] "%(value)s mhillean"
msgstr[1] "%(value)s mhillean"
msgstr[2] "%(value)s milleanan"
msgstr[3] "%(value)s millean"

#, python-format
msgid "%(value)s billion"
msgid_plural "%(value)s billion"
msgstr[0] "%(value)s bhillean"
msgstr[1] "%(value)s bhillean"
msgstr[2] "%(value)s billeanan"
msgstr[3] "%(value)s billean"

#, python-format
msgid "%(value)s trillion"
msgid_plural "%(value)s trillion"
msgstr[0] "%(value)s trillean"
msgstr[1] "%(value)s thrillean"
msgstr[2] "%(value)s trilleanan"
msgstr[3] "%(value)s trillean"

#, python-format
msgid "%(value)s quadrillion"
msgid_plural "%(value)s quadrillion"
msgstr[0] "%(value)s quadrillean"
msgstr[1] "%(value)s quadrillean"
msgstr[2] "%(value)s quadrilleanan"
msgstr[3] "%(value)s quadrillean"

#, python-format
msgid "%(value)s quintillion"
msgid_plural "%(value)s quintillion"
msgstr[0] "%(value)s quintillean"
msgstr[1] "%(value)s quintillean"
msgstr[2] "%(value)s quintilleanan"
msgstr[3] "%(value)s quintillean"

#, python-format
msgid "%(value)s sextillion"
msgid_plural "%(value)s sextillion"
msgstr[0] "%(value)s sextillean"
msgstr[1] "%(value)s shextillean"
msgstr[2] "%(value)s sextilleanan"
msgstr[3] "%(value)s sextillean"

#, python-format
msgid "%(value)s septillion"
msgid_plural "%(value)s septillion"
msgstr[0] "%(value)s septillean"
msgstr[1] "%(value)s sheptillean"
msgstr[2] "%(value)s septilleanan"
msgstr[3] "%(value)s septillean"

#, python-format
msgid "%(value)s octillion"
msgid_plural "%(value)s octillion"
msgstr[0] "%(value)s octillean"
msgstr[1] "%(value)s octillean"
msgstr[2] "%(value)s octilleanan"
msgstr[3] "%(value)s octillean"

#, python-format
msgid "%(value)s nonillion"
msgid_plural "%(value)s nonillion"
msgstr[0] "%(value)s nonillean"
msgstr[1] "%(value)s nonillean"
msgstr[2] "%(value)s nonilleanan"
msgstr[3] "%(value)s nonillean"

#, python-format
msgid "%(value)s decillion"
msgid_plural "%(value)s decillion"
msgstr[0] "%(value)s decillean"
msgstr[1] "%(value)s dhecillean"
msgstr[2] "%(value)s decilleanan"
msgstr[3] "%(value)s decillean"

#, python-format
msgid "%(value)s googol"
msgid_plural "%(value)s googol"
msgstr[0] "%(value)s ghoogol"
msgstr[1] "%(value)s ghoogol"
msgstr[2] "%(value)s googolan"
msgstr[3] "%(value)s googol"

msgid "one"
msgstr "aon"

msgid "two"
msgstr "dà"

msgid "three"
msgstr "trì"

msgid "four"
msgstr "ceithir"

msgid "five"
msgstr "còig"

msgid "six"
msgstr "sia"

msgid "seven"
msgstr "seachd"

msgid "eight"
msgstr "ochd"

msgid "nine"
msgstr "naoidh"

msgid "today"
msgstr "an-diugh"

msgid "tomorrow"
msgstr "a-màireach"

msgid "yesterday"
msgstr "an-dè"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s ago"
msgstr "%(delta)s air ais"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour ago"
msgid_plural "%(count)s hours ago"
msgstr[0] "%(count)s uair a thìde air ais"
msgstr[1] "%(count)s uair a thìde air ais"
msgstr[2] "%(count)s uairean a thìde air ais"
msgstr[3] "%(count)s uair a thìde air ais"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute ago"
msgid_plural "%(count)s minutes ago"
msgstr[0] "%(count)s mhionaid air ais"
msgstr[1] "%(count)s mhionaid air ais"
msgstr[2] "%(count)s mionaidean air ais"
msgstr[3] "%(count)s mionaid air ais"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second ago"
msgid_plural "%(count)s seconds ago"
msgstr[0] "%(count)s diog air ais"
msgstr[1] "%(count)s dhiog air ais"
msgstr[2] "%(count)s diogan air ais"
msgstr[3] "%(count)s diog air ais"

msgid "now"
msgstr "an-dràsta"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a second from now"
msgid_plural "%(count)s seconds from now"
msgstr[0] "an ceann %(count)s diog"
msgstr[1] "an ceann %(count)s dhiog"
msgstr[2] "an ceann %(count)s diogan"
msgstr[3] "an ceann %(count)s diog"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "a minute from now"
msgid_plural "%(count)s minutes from now"
msgstr[0] "an ceann %(count)s mhionaid"
msgstr[1] "an ceann %(count)s mhionaid"
msgstr[2] "an ceann %(count)s mionaidean"
msgstr[3] "an ceann %(count)s mionaid"

#. Translators: please keep a non-breaking space (U+00A0) between count
#. and time unit.
#, python-format
msgid "an hour from now"
msgid_plural "%(count)s hours from now"
msgstr[0] "an ceann %(count)s uair a thìde"
msgstr[1] "an ceann %(count)s uair a thìde"
msgstr[2] "an ceann %(count)s uairean a thìde"
msgstr[3] "an ceann %(count)s uair a thìde"

#. Translators: delta will contain a string like '2 months' or '1 month, 2
#. weeks'
#, python-format
msgid "%(delta)s from now"
msgstr "an ceann %(delta)s"

#. Translators: 'naturaltime-past' strings will be included in '%(delta)s ago'
#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d bhliadhna"
msgstr[1] "%(num)d bhliadhna"
msgstr[2] "%(num)d bliadhnaichean"
msgstr[3] "%(num)d bliadhna"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mhìos"
msgstr[1] "%(num)d mhìos"
msgstr[2] "%(num)d mìosan"
msgstr[3] "%(num)d mìos"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d seachdain"
msgstr[1] "%(num)d sheachdain"
msgstr[2] "%(num)d seachdainean"
msgstr[3] "%(num)d seachdain"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d latha"
msgstr[1] "%(num)d latha"
msgstr[2] "%(num)d làithean"
msgstr[3] "%(num)d latha"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d uair a thìde"
msgstr[1] "%(num)d uair a thìde"
msgstr[2] "%(num)d uairean a thìde"
msgstr[3] "%(num)d uair a thìde"

#, python-format
msgctxt "naturaltime-past"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d mhionaid"
msgstr[1] "%(num)d mhionaid"
msgstr[2] "%(num)d mionaidean"
msgstr[3] "%(num)d mionaid"

#. Translators: 'naturaltime-future' strings will be included in '%(delta)s
#. from now'
#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d year"
msgid_plural "%(num)d years"
msgstr[0] "%(num)d bhliadhna"
msgstr[1] "%(num)d bhliadhna"
msgstr[2] "%(num)d bliadhnaichean"
msgstr[3] "%(num)d bliadhna"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d month"
msgid_plural "%(num)d months"
msgstr[0] "%(num)d mhìos"
msgstr[1] "%(num)d mhìos"
msgstr[2] "%(num)d mìosan"
msgstr[3] "%(num)d mìos"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d week"
msgid_plural "%(num)d weeks"
msgstr[0] "%(num)d seachdain"
msgstr[1] "%(num)d sheachdain"
msgstr[2] "%(num)d seachdainean"
msgstr[3] "%(num)d seachdain"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d day"
msgid_plural "%(num)d days"
msgstr[0] "%(num)d latha"
msgstr[1] "%(num)d latha"
msgstr[2] "%(num)d làithean"
msgstr[3] "%(num)d latha"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d hour"
msgid_plural "%(num)d hours"
msgstr[0] "%(num)d uair a thìde"
msgstr[1] "%(num)d uair a thìde"
msgstr[2] "%(num)d uairean a thìde"
msgstr[3] "%(num)d uair a thìde"

#, python-format
msgctxt "naturaltime-future"
msgid "%(num)d minute"
msgid_plural "%(num)d minutes"
msgstr[0] "%(num)d mhionaid"
msgstr[1] "%(num)d mhionaid"
msgstr[2] "%(num)d mionaidean"
msgstr[3] "%(num)d mionaid"
